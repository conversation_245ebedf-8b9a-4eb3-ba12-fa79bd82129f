import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';
import { getServerSession } from "next-auth/next";
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';

// GET endpoint do pobierania treści audytu domeny (chroniony dostęp)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await connectDB();

    const { id } = await params;

    // Znajdź domenę i sprawdź własność
    const domain = await Domain.findOne({
      _id: id,
      userId: session.user.id
    }).select('auditContent lighthouseContent');

    if (!domain) {
      return NextResponse.json(
        {
          success: false,
          error: 'Domain not found or access denied'
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      auditContent: domain.auditContent || '',
      lighthouseContent: domain.lighthouseContent || ''
    });

  } catch (error) {
    console.error('Błąd pobierania treści audytu:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error occurred while fetching audit content'
      },
      { status: 500 }
    );
  }
}
