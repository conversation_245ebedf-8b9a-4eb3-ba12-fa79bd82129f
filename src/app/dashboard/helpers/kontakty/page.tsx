"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  Loader2,
  Plus,
  Search,
  Download,
  Trash2,
  Edit,
  Building,
  User,
  Mail,
  Phone,
  MapPin,
  FileText,
} from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Contact {
  id: string;
  contact_companyName: string;
  contact_contactPerson: string;
  contact_email: string;
  contact_phone: string;
  contact_category: string;
  contact_address: string;
  contact_notes: string;
  createdAt: string;
  updatedAt: string;
}

interface ContactFormData {
  contact_companyName: string;
  contact_contactPerson: string;
  contact_email: string;
  contact_phone: string;
  contact_category: string;
  contact_address: string;
  contact_notes: string;
}

export default function KontaktyPage() {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddingContact, setIsAddingContact] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const [formData, setFormData] = useState<ContactFormData>({
    contact_companyName: "",
    contact_contactPerson: "",
    contact_email: "",
    contact_phone: "",
    contact_category: "",
    contact_address: "",
    contact_notes: "",
  });

  const fetchContacts = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      if (selectedCategory && selectedCategory !== "all") params.append("category", selectedCategory);
      if (searchTerm) params.append("search", searchTerm);

      const response = await fetch(`/api/contacts?${params.toString()}`);

      if (!response.ok) {
        throw new Error("Błąd pobierania kontaktów");
      }

      const data = await response.json();
      if (data.success) {
        setContacts(data.contacts);
        setCategories(data.categories);
      }
    } catch (error) {
      console.error("Błąd pobierania kontaktów:", error);
      toast.error("Nie udało się pobrać kontaktów");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchContacts();
  }, [selectedCategory, searchTerm]);

  const handleAddContact = async () => {
    if (!formData.contact_companyName.trim() || !formData.contact_contactPerson.trim() ||
        !formData.contact_email.trim() || !formData.contact_phone.trim() ||
        !formData.contact_category.trim() || !formData.contact_address.trim()) {
      toast.error("Wszystkie pola są wymagane");
      return;
    }

    setIsAddingContact(true);
    try {
      const response = await fetch("/api/contacts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error("Błąd dodawania kontaktu");
      }

      const data = await response.json();
      if (data.success) {
        toast.success("Kontakt został dodany");
        setFormData({
          contact_companyName: "",
          contact_contactPerson: "",
          contact_email: "",
          contact_phone: "",
          contact_category: "",
          contact_address: "",
          contact_notes: "",
        });
        setIsDialogOpen(false);
        fetchContacts();
      }
    } catch (error) {
      console.error("Błąd dodawania kontaktu:", error);
      toast.error("Nie udało się dodać kontaktu");
    } finally {
      setIsAddingContact(false);
    }
  };

  const handleDeleteContact = async (contactId: string) => {
    if (!confirm("Czy na pewno chcesz usunąć ten kontakt?")) {
      return;
    }

    try {
      const response = await fetch(`/api/contacts/${contactId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Błąd usuwania kontaktu");
      }

      const data = await response.json();
      if (data.success) {
        toast.success("Kontakt został usunięty");
        fetchContacts();
      }
    } catch (error) {
      console.error("Błąd usuwania kontaktu:", error);
      toast.error("Nie udało się usunąć kontaktu");
    }
  };

  const handleExport = async (format: string = "csv") => {
    try {
      const params = new URLSearchParams();
      params.append("format", format);
      if (selectedCategory && selectedCategory !== "all") params.append("category", selectedCategory);

      const response = await fetch(`/api/contacts/export?${params.toString()}`);

      if (!response.ok) {
        throw new Error("Błąd eksportu kontaktów");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.style.display = "none";
      a.href = url;
      a.download = `kontakty_${new Date().toISOString().split("T")[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success(`Kontakty zostały wyeksportowane do pliku ${format.toUpperCase()}`);
    } catch (error) {
      console.error("Błąd eksportu:", error);
      toast.error("Nie udało się wyeksportować kontaktów");
    }
  };

  const filteredContacts = contacts.filter(contact =>
    contact.contact_companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.contact_contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.contact_email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Nagłówek */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Kontakty</h1>
          <p className="text-muted-foreground mt-2">
            Zarządzaj bazą kontaktów firmowych
          </p>
        </div>

        <div className="flex gap-2">
          <Button onClick={() => handleExport("csv")} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Eksportuj CSV
          </Button>

          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Dodaj kontakt
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Dodaj nowy kontakt</DialogTitle>
              </DialogHeader>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="companyName">Nazwa firmy *</Label>
                  <Input
                    id="companyName"
                    value={formData.contact_companyName}
                    onChange={(e) => setFormData({...formData, contact_companyName: e.target.value})}
                    placeholder="Wprowadź nazwę firmy"
                  />
                </div>

                <div>
                  <Label htmlFor="contactPerson">Osoba kontaktowa *</Label>
                  <Input
                    id="contactPerson"
                    value={formData.contact_contactPerson}
                    onChange={(e) => setFormData({...formData, contact_contactPerson: e.target.value})}
                    placeholder="Imię i nazwisko"
                  />
                </div>

                <div>
                  <Label htmlFor="email">Email *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.contact_email}
                    onChange={(e) => setFormData({...formData, contact_email: e.target.value})}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <Label htmlFor="phone">Telefon *</Label>
                  <Input
                    id="phone"
                    value={formData.contact_phone}
                    onChange={(e) => setFormData({...formData, contact_phone: e.target.value})}
                    placeholder="+48 123 456 789"
                  />
                </div>

                <div>
                  <Label htmlFor="category">Kategoria *</Label>
                  <Input
                    id="category"
                    value={formData.contact_category}
                    onChange={(e) => setFormData({...formData, contact_category: e.target.value})}
                    placeholder="np. Klient, Dostawca, Partner"
                  />
                </div>

                <div>
                  <Label htmlFor="address">Adres *</Label>
                  <Input
                    id="address"
                    value={formData.contact_address}
                    onChange={(e) => setFormData({...formData, contact_address: e.target.value})}
                    placeholder="Pełny adres"
                  />
                </div>

                <div className="col-span-2">
                  <Label htmlFor="notes">Notatki</Label>
                  <Textarea
                    id="notes"
                    value={formData.contact_notes}
                    onChange={(e) => setFormData({...formData, contact_notes: e.target.value})}
                    placeholder="Dodatkowe informacje..."
                    rows={3}
                  />
                </div>
              </div>

              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Anuluj
                </Button>
                <Button onClick={handleAddContact} disabled={isAddingContact}>
                  {isAddingContact ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Dodawanie...
                    </>
                  ) : (
                    "Dodaj kontakt"
                  )}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filtry */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Szukaj kontaktów..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <Select value={selectedCategory} onValueChange={(value) => setSelectedCategory(value === "all" ? "" : value)}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Wszystkie kategorie" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Wszystkie kategorie</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Lista kontaktów */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Lista kontaktów ({filteredContacts.length})</span>
            {categories.length > 0 && (
              <div className="flex gap-2">
                {categories.map((category) => (
                  <Badge key={category} variant="secondary">
                    {category}
                  </Badge>
                ))}
              </div>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Ładowanie kontaktów...</span>
            </div>
          ) : filteredContacts.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {searchTerm || (selectedCategory && selectedCategory !== "all") ?
                "Nie znaleziono kontaktów spełniających kryteria wyszukiwania" :
                "Brak kontaktów. Dodaj pierwszy kontakt używając przycisku powyżej."
              }
            </div>
          ) : (
            <div className="grid gap-4">
              {filteredContacts.map((contact) => (
                <Card key={contact.id} className="border-l-4 border-l-blue-500">
                  <CardContent className="pt-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <Building className="h-4 w-4 text-muted-foreground" />
                            <span className="font-semibold">{contact.contact_companyName}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-muted-foreground" />
                            <span>{contact.contact_contactPerson}</span>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <Mail className="h-4 w-4 text-muted-foreground" />
                            <a
                              href={`mailto:${contact.contact_email}`}
                              className="text-blue-600 hover:underline"
                            >
                              {contact.contact_email}
                            </a>
                          </div>
                          <div className="flex items-center gap-2">
                            <Phone className="h-4 w-4 text-muted-foreground" />
                            <a
                              href={`tel:${contact.contact_phone}`}
                              className="text-blue-600 hover:underline"
                            >
                              {contact.contact_phone}
                            </a>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">{contact.contact_address}</span>
                          </div>
                          <Badge variant="outline">{contact.contact_category}</Badge>
                        </div>
                      </div>

                      <div className="flex gap-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteContact(contact.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {contact.contact_notes && (
                      <div className="mt-4 pt-4 border-t">
                        <div className="flex items-start gap-2">
                          <FileText className="h-4 w-4 text-muted-foreground mt-0.5" />
                          <span className="text-sm text-muted-foreground">
                            {contact.contact_notes}
                          </span>
                        </div>
                      </div>
                    )}

                    <div className="mt-4 pt-4 border-t text-xs text-muted-foreground">
                      Utworzono: {new Date(contact.createdAt).toLocaleString("pl-PL")}
                      {contact.updatedAt !== contact.createdAt && (
                        <span className="ml-4">
                          Zaktualizowano: {new Date(contact.updatedAt).toLocaleString("pl-PL")}
                        </span>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
