"use client";

import { useState, useEffect, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogOverlay,
  DialogPortal,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import {
  Search,
  Link as LinkIcon,
  Globe,
  Loader2,
  ChevronLeft,
  ChevronRight,
  Plus,
} from "lucide-react";

const searchSchema = z.object({
  query: z
    .string()
    .min(1, "Query jest wymagane")
    .max(500, "Query nie może być dłuższe niż 500 znaków"),
  location: z
    .string()
    .max(200, "Lokalizacja nie może być dłuższa niż 200 znaków")
    .optional(),
  page: z
    .number()
    .min(1, "Strona musi być większa niż 0")
    .max(100, "Maksymalnie 100 stron")
    .optional(),
});

type SearchFormData = z.infer<typeof searchSchema>;

interface Category {
  id: string;
  name: string;
  description?: string;
  isDefault: boolean;
}

interface SearchResult {
  id: string;
  url: string;
  title: string;
  snippet?: string;
}

interface SkippedLink {
  url: string;
  title: string;
  reason: string;
}

interface SearchResponse {
  success: boolean;
  searchQuery: {
    id: string;
    query: string;
    category: string;
    location?: string;
    page: number;
  };
  linksCount: number;
  domainsCount: number;
  skippedCount: number;
  totalProcessed: number;
  results: SearchResult[];
  skippedLinks: SkippedLink[];
}

export default function AgregatorPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSearchModalOpen, setIsSearchModalOpen] = useState(false);
  const [searchAbortController, setSearchAbortController] = useState<AbortController | null>(null);
  const [searchResults, setSearchResults] = useState<SearchResponse | null>(
    null
  );
  const [isManualPage, setIsManualPage] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [resultsPerPage, setResultsPerPage] = useState(10); // Liczba wyników na stronę
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [isManualDialogOpen, setIsManualDialogOpen] = useState(false);
  const [manualDomainForm, setManualDomainForm] = useState({
    url: "",
    category: "",
  });
  const [isAddingDomain, setIsAddingDomain] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<SearchFormData>({
    resolver: zodResolver(searchSchema),
  });

  // Funkcja do pobierania kategorii
  const fetchCategories = useCallback(async () => {
    try {
      const response = await fetch("/api/categories");
      if (!response.ok) {
        throw new Error("Błąd pobierania kategorii");
      }
      const data = await response.json();
      setCategories(data.categories);

      // Automatycznie wybierz pierwszą kategorię jako domyślną
      if (data.categories.length > 0 && !selectedCategory) {
        setSelectedCategory(data.categories[0].name);
      }
    } catch (error) {
      console.error("Błąd pobierania kategorii:", error);
      toast.error("Wystąpił błąd podczas pobierania kategorii");
    }
  }, [setCategories, selectedCategory, setSelectedCategory]); // Added dependencies

  // Pobierz kategorie przy załadowaniu komponentu
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]); // Updated dependencies

  // Funkcja do ręcznego dodawania domeny
  const handleManualDomainAdd = async () => {
    if (!manualDomainForm.url.trim() || !manualDomainForm.category.trim()) {
      toast.error("URL i kategoria są wymagane");
      return;
    }

    setIsAddingDomain(true);

    try {
      const response = await fetch("/api/domains/manual", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(manualDomainForm),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Błąd dodawania domeny");
      }

      const data = await response.json();
      toast.success(data.message || "Domena została dodana pomyślnie");

      // Reset formularza i zamknij modal
      setManualDomainForm({ url: "", category: "" });
      setIsManualDialogOpen(false);
    } catch (error: unknown) {
      console.error("Błąd dodawania domeny:", error);
      toast.error(error instanceof Error ? error.message : "Wystąpił błąd podczas dodawania domeny");
    } finally {
      setIsAddingDomain(false);
    }
  };

  // Otwórz modal z pierwszą kategorią jako domyślną
  const openManualDialog = () => {
    if (categories.length > 0) {
      setManualDomainForm({
        url: "",
        category: categories[0].name,
      });
    }
    setIsManualDialogOpen(true);
  };

  // Funkcje paginacji
  const totalPages = searchResults
    ? Math.ceil(searchResults.results.length / resultsPerPage)
    : 0;
  const startIndex = (currentPage - 1) * resultsPerPage;
  const endIndex = startIndex + resultsPerPage;
  const currentResults = searchResults
    ? searchResults.results.slice(startIndex, endIndex)
    : [];

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleResultsPerPageChange = (newResultsPerPage: number) => {
    setResultsPerPage(newResultsPerPage);
    setCurrentPage(1); // Reset do pierwszej strony
  };

  const handleCancelSearch = () => {
    if (searchAbortController) {
      searchAbortController.abort();
    }
    setIsLoading(false);
    setIsSearchModalOpen(false);
    setSearchAbortController(null);
    toast.info("Wyszukiwanie zostało anulowane");
  };

  const onSubmit = async (data: SearchFormData) => {
    setIsLoading(true);
    setIsSearchModalOpen(true); // Pokaż okno loading modal
    setCurrentPage(1); // Reset paginacji przy nowym wyszukiwaniu

    // Sprawdź czy kategoria została wybrana
    if (!selectedCategory || selectedCategory === "no-categories") {
      toast.error("Proszę wybrać kategorię");
      setIsLoading(false);
      setIsSearchModalOpen(false);
      return;
    }

    // Ustaw czy strona została wybrana ręcznie
    setIsManualPage(!!data.page && data.page > 0);

    // Utwórz AbortController dla tego wyszukiwania
    const abortController = new AbortController();
    setSearchAbortController(abortController);

    try {
      const response = await fetch("/api/search", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
          category: selectedCategory,
        }),
        signal: abortController.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || "Wystąpił błąd podczas wyszukiwania"
        );
      }

      const result: SearchResponse = await response.json();
      setSearchResults(result);

      const locationInfo = result.searchQuery.location ? ` (lokalizacja: ${result.searchQuery.location})` : '';
      const pageInfo = isManualPage ? ` (wybrana strona ${result.searchQuery.page})` : ` (strona ${result.searchQuery.page})`;
      const message =
        result.skippedCount > 0
          ? `Wyszukiwanie zakończone! Dodano ${result.linksCount} nowych linków z ${result.domainsCount} domen. Pominięto ${result.skippedCount} linków (domeny już istnieją).${pageInfo}${locationInfo}`
          : `Wyszukiwanie zakończone! Znaleziono ${result.linksCount} linków z ${result.domainsCount} domen${pageInfo}${locationInfo}`;

      // Zamknij okno loading modal przed pokazaniem komunikatu
      setIsSearchModalOpen(false);
      setSearchAbortController(null);
      toast.success(message);
    } catch (error) {
      console.error("Błąd wyszukiwania:", error);
      setIsSearchModalOpen(false); // Zamknij okno loading modal w przypadku błędu
      setSearchAbortController(null);

      // Sprawdź czy błąd to anulowanie
      if (error instanceof Error && error.name === 'AbortError') {
        // Nie pokazuj błędu dla anulowanego wyszukiwania
        return;
      }

      toast.error(
        error instanceof Error
          ? error.message
          : "Wystąpił błąd podczas wyszukiwania"
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Komponent Loading Modal
  const LoadingModal = () => (
    <Dialog open={isSearchModalOpen} onOpenChange={() => {}}>
      <DialogPortal>
        <DialogOverlay className="bg-black/80 backdrop-blur-sm" />
        <DialogContent className="sm:max-w-md [&>button]:hidden border-primary/20">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-3 text-lg">
              <Search className="h-6 w-6 text-primary" />
              Wyszukiwanie w toku
            </DialogTitle>
            <DialogDescription className="text-center">
              Proszę czekać do końca akcji pobierania linków...
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center justify-center py-8">
            <div className="flex flex-col items-center gap-6">
              <div className="relative">
                <div className="w-20 h-20 border-4 border-primary/20 border-t-primary rounded-full animate-spin"></div>
                <div className="absolute inset-2 w-16 h-16 border-4 border-transparent border-t-primary/60 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
                <div className="absolute inset-4 w-12 h-12 border-2 border-transparent border-t-primary/40 rounded-full animate-spin" style={{ animationDuration: '2s' }}></div>
              </div>
              <div className="text-center space-y-2">
                <p className="text-sm font-medium text-foreground">
                  Analizowanie wyników wyszukiwania...
                </p>
                <p className="text-xs text-muted-foreground">
                  Nie zamykaj tej strony podczas wyszukiwania
                </p>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={handleCancelSearch}
              className="w-full"
            >
              Anuluj wyszukiwanie
            </Button>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );

  return (
    <div className="space-y-8">
      {/* Nagłówek */}
      <div>
        <h1 className="text-3xl font-bold text-foreground">
          Agregator Wyszukiwania
        </h1>
        <p className="text-muted-foreground mt-2">
          Wyszukuj i agreguj linki z Google za pomocą Serper API. Wyniki są
          automatycznie kategoryzowane i zapisywane.
        </p>
      </div>

      {/* Formularz wyszukiwania */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Nowe Wyszukiwanie
          </CardTitle>
          <CardDescription>
            Wprowadź zapytanie i kategorię. Możesz wybrać konkretną stronę lub pozostawić puste
            dla automatycznej paginacji (kolejne strony dla powtarzających się zapytań).
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="query">Zapytanie wyszukiwania</Label>
                <Input
                  id="query"
                  placeholder="np. Next.js tutorial"
                  {...register("query")}
                  disabled={isLoading}
                />
                {errors.query && (
                  <p className="text-sm text-destructive">
                    {errors.query.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="category">Kategoria</Label>
                <Select
                  value={selectedCategory}
                  onValueChange={(value) => {
                    setSelectedCategory(value);
                  }}
                  disabled={isLoading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Wybierz kategorię" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.length === 0 ? (
                      <SelectItem value="no-categories" disabled>
                        Brak kategorii. Dodaj kategorie w ustawieniach.
                      </SelectItem>
                    ) : (
                      categories.map((category) => (
                        <SelectItem key={category.id} value={category.name}>
                          {category.name}
                          {category.description && (
                            <span className="text-muted-foreground ml-2">
                              - {category.description}
                            </span>
                          )}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                {(!selectedCategory || selectedCategory === "no-categories") &&
                  categories.length === 0 && (
                    <p className="text-sm text-destructive">
                      Kategoria jest wymagana
                    </p>
                  )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="location">Lokalizacja (opcjonalnie)</Label>
                <Input
                  id="location"
                  placeholder="np. Warsaw, Poland"
                  {...register("location")}
                  disabled={isLoading}
                />
                {errors.location && (
                  <p className="text-sm text-destructive">
                    {errors.location.message}
                  </p>
                )}
                <p className="text-xs text-muted-foreground">
                  Określ lokalizację dla wyszukiwania (miasto, kraj)
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="page">Strona (opcjonalnie)</Label>
                <Input
                  id="page"
                  type="number"
                  min="1"
                  max="100"
                  placeholder="1"
                  {...register("page", { valueAsNumber: true })}
                  disabled={isLoading}
                />
                {errors.page && (
                  <p className="text-sm text-destructive">
                    {errors.page.message}
                  </p>
                )}
                <p className="text-xs text-muted-foreground">
                  Wybierz konkretną stronę (1-100)
                </p>
              </div>
            </div>

            <div className="flex gap-2">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Wyszukiwanie...
                  </>
                ) : (
                  <>
                    <Search className="mr-2 h-4 w-4" />
                    Wyszukaj
                  </>
                )}
              </Button>

              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  reset();
                  setSelectedCategory("");
                  setSearchResults(null);
                  setCurrentPage(1);
                }}
                disabled={isLoading}
              >
                Wyczyść
              </Button>

              <Button
                type="button"
                variant="secondary"
                onClick={openManualDialog}
                disabled={isLoading || categories.length === 0}
              >
                <Plus className="mr-2 h-4 w-4" />
                Dodaj domenę
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Wyniki wyszukiwania */}
      {searchResults && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <LinkIcon className="h-5 w-5" />
              Wyniki Wyszukiwania
            </CardTitle>
            <CardDescription>
              Zapytanie: {searchResults.searchQuery.query} | Kategoria:{ }
              {searchResults.searchQuery.category}
              {searchResults.searchQuery.location && (
                <> | Lokalizacja: {searchResults.searchQuery.location}</>
              )} | Strona:{" "}
              {searchResults.searchQuery.page}{isManualPage && " (wybrana ręcznie)"} | Przetworzono:{" "}
              {searchResults.totalProcessed} wyników | Dodano:{" "}
              {searchResults.linksCount} linków z {searchResults.domainsCount}{" "}
              domen
              {searchResults.skippedCount > 0 && (
                <span className="text-orange-600 dark:text-orange-400">
                  {" "}
                  | Pominięto: {searchResults.skippedCount} (domeny już
                  istnieją)
                </span>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {currentResults.map((result) => (
                <div
                  key={result.id}
                  className="border rounded-lg p-4 hover:bg-muted/50 transition-colors"
                >
                  <h3 className="font-medium text-foreground mb-2">
                    <a
                      href={result.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:text-primary transition-colors"
                    >
                      {result.title}
                    </a>
                  </h3>
                  <p className="text-sm text-muted-foreground mb-2">
                    {result.snippet}
                  </p>
                  <p className="text-xs text-muted-foreground flex items-center gap-1">
                    <Globe className="h-3 w-3" />
                    {result.url}
                  </p>
                </div>
              ))}
            </div>

            {/* Paginacja */}
            {searchResults && searchResults.results.length > 0 && (
              <div className="mt-6 border-t pt-4">
                <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                  <div className="flex items-center gap-4">
                    <div className="text-sm text-muted-foreground">
                      Strona {currentPage} z {totalPages} (
                      {searchResults.results.length} wyników, pokazano{" "}
                      {currentResults.length})
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">
                        Wyników na stronę:
                      </span>
                      <select
                        value={resultsPerPage}
                        onChange={(e) =>
                          handleResultsPerPageChange(Number(e.target.value))
                        }
                        className="px-2 py-1 border border-input bg-background rounded text-sm"
                      >
                        <option value={5}>5</option>
                        <option value={10}>10</option>
                        <option value={20}>20</option>
                        <option value={50}>50</option>
                      </select>
                    </div>
                  </div>

                  {totalPages > 1 && (
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                      >
                        <ChevronLeft className="h-4 w-4" />
                        Poprzednia
                      </Button>

                      {/* Numery stron */}
                      <div className="flex items-center gap-1">
                        {Array.from(
                          { length: Math.min(5, totalPages) },
                          (_, i) => {
                            let pageNum;
                            if (totalPages <= 5) {
                              pageNum = i + 1;
                            } else if (currentPage <= 3) {
                              pageNum = i + 1;
                            } else if (currentPage >= totalPages - 2) {
                              pageNum = totalPages - 4 + i;
                            } else {
                              pageNum = currentPage - 2 + i;
                            }

                            return (
                              <Button
                                key={pageNum}
                                variant={
                                  currentPage === pageNum
                                    ? "default"
                                    : "outline"
                                }
                                size="sm"
                                onClick={() => handlePageChange(pageNum)}
                                className="w-8 h-8 p-0"
                              >
                                {pageNum}
                              </Button>
                            );
                          }
                        )}
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                      >
                        Następna
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Modal do ręcznego dodawania domeny */}
      <Dialog open={isManualDialogOpen} onOpenChange={setIsManualDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Dodaj domenę ręcznie</DialogTitle>
            <DialogDescription>
              Wprowadź URL domeny i wybierz kategorię. Domena zostanie dodana do
              systemu.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="manual-url">URL domeny</Label>
              <Input
                id="manual-url"
                placeholder="np. example.com lub https://example.com"
                value={manualDomainForm.url}
                onChange={(e) =>
                  setManualDomainForm({
                    ...manualDomainForm,
                    url: e.target.value,
                  })
                }
                disabled={isAddingDomain}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="manual-category">Kategoria</Label>
              <Select
                value={manualDomainForm.category}
                onValueChange={(value) =>
                  setManualDomainForm({
                    ...manualDomainForm,
                    category: value,
                  })
                }
                disabled={isAddingDomain}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Wybierz kategorię" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.name}>
                      {category.name}
                      {category.description && (
                        <span className="text-muted-foreground ml-2">
                          - {category.description}
                        </span>
                      )}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsManualDialogOpen(false)}
              disabled={isAddingDomain}
            >
              Anuluj
            </Button>
            <Button
              type="button"
              onClick={handleManualDomainAdd}
              disabled={isAddingDomain}
            >
              {isAddingDomain ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Dodawanie...
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  Dodaj domenę
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Szybkie akcje */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <LinkIcon className="h-5 w-5" />
              Zapisane Linki
            </CardTitle>
            <CardDescription>
              Przeglądaj wszystkie zapisane linki z wyszukiwań
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild className="w-full">
              <a href="/dashboard/agregator/links">Przeglądaj Linki</a>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Loading Modal */}
      <LoadingModal />
    </div>
  );
}
