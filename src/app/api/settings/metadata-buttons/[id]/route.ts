import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next'; // Changed import path
import { Session } from 'next-auth'; // Added Session import
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Settings from '@/models/Settings';

interface MetadataButton {
  id: string;
  label: string;
  key: string;
  value: string | number | boolean; // Can be string, number, boolean, etc.
  icon: string;
  description?: string;
  order?: number;
}

// PUT - Aktualizuj przycisk metadata
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null; // Explicitly cast session

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;

    // Validate the id parameter
    if (!id || typeof id !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Nieprawidłowy identyfikator przycisku' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { label, key, value, icon, description, order } = body;

    // Walidacja
    if (!label || !label.trim()) {
      return NextResponse.json(
        { success: false, error: 'Etykieta przycisku jest wymagana' },
        { status: 400 }
      );
    }

    if (!key || !key.trim()) {
      return NextResponse.json(
        { success: false, error: 'Klucz metadata jest wymagany' },
        { status: 400 }
      );
    }

    if (value === undefined || value === null) {
      return NextResponse.json(
        { success: false, error: 'Wartość jest wymagana' },
        { status: 400 }
      );
    }

    if (!icon || !icon.trim()) {
      return NextResponse.json(
        { success: false, error: 'Ikona jest wymagana' },
        { status: 400 }
      );
    }

    await connectDB();

    const settings = await Settings.findOne({ userId: session.user.id });

    if (!settings) {
      return NextResponse.json(
        { success: false, error: 'Ustawienia nie zostały znalezione' },
        { status: 404 }
      );
    }

    // Znajdź przycisk do aktualizacji
    const buttonIndex = settings.metadataButtons.findIndex((btn: MetadataButton) => btn.id === id);
    if (buttonIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'Przycisk nie został znaleziony' },
        { status: 404 }
      );
    }

    // Sprawdź czy nowy klucz nie koliduje z innymi (jeśli się zmienił)
    const currentButton = settings.metadataButtons[buttonIndex];

    if (key.trim() !== currentButton.key) {
      const existingButton = settings.metadataButtons.find((btn: MetadataButton) => btn.key === key.trim() && btn.id !== id);
      if (existingButton) {
        return NextResponse.json(
          { success: false, error: 'Przycisk z tym kluczem już istnieje' },
          { status: 400 }
        );
      }
    }

    // Aktualizuj przycisk - explicitly preserve all required fields
    settings.metadataButtons[buttonIndex].id = id;
    settings.metadataButtons[buttonIndex].label = label.trim();
    settings.metadataButtons[buttonIndex].key = key.trim();
    settings.metadataButtons[buttonIndex].value = value;
    settings.metadataButtons[buttonIndex].icon = icon.trim();
    settings.metadataButtons[buttonIndex].description = description?.trim() || '';
    if (order !== undefined) {
      settings.metadataButtons[buttonIndex].order = order;
    }

    await settings.save();

    return NextResponse.json({
      success: true,
      message: 'Przycisk metadata został zaktualizowany',
      button: settings.metadataButtons[buttonIndex]
    });

  } catch (error) {
    console.error('Błąd aktualizacji przycisku metadata:', error);
    return NextResponse.json(
      { success: false, error: 'Wystąpił błąd podczas aktualizacji przycisku metadata' },
      { status: 500 }
    );
  }
}

// DELETE - Usuń przycisk metadata
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null; // Explicitly cast session

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;

    await connectDB();

    const settings = await Settings.findOne({ userId: session.user.id });

    if (!settings) {
      return NextResponse.json(
        { success: false, error: 'Ustawienia nie zostały znalezione' },
        { status: 404 }
      );
    }

    // Znajdź przycisk do usunięcia
    const buttonIndex = settings.metadataButtons.findIndex((btn: MetadataButton) => btn.id === id);
    if (buttonIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'Przycisk nie został znaleziony' },
        { status: 404 }
      );
    }

    // Usuń przycisk
    settings.metadataButtons.splice(buttonIndex, 1);
    await settings.save();

    return NextResponse.json({
      success: true,
      message: 'Przycisk metadata został usunięty'
    });

  } catch (error) {
    console.error('Błąd usuwania przycisku metadata:', error);
    return NextResponse.json(
      { success: false, error: 'Wystąpił błąd podczas usuwania przycisku metadata' },
      { status: 500 }
    );
  }
}
