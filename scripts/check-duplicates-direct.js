// Skrypt do bezpośredniego sprawdzenia duplikatów w bazie danych
// Uruchom: node scripts/check-duplicates-direct.js

const { MongoClient } = require("mongodb");
const fs = require("fs");
const path = require("path");

// Funkcja do odczytania zmiennych środowiskowych z .env
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, "..", ".env");
    const envContent = fs.readFileSync(envPath, "utf8");
    const lines = envContent.split("\n");

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith("#")) {
        const [key, ...valueParts] = trimmedLine.split("=");
        if (key && valueParts.length > 0) {
          process.env[key.trim()] = valueParts.join("=").trim();
        }
      }
    }
  } catch (error) {
    console.error("Nie można odczytać pliku .env:", error.message);
  }
}

loadEnvFile();

async function checkDuplicates() {
  const client = new MongoClient(process.env.MONGODB_URI);

  try {
    await client.connect();
    console.log("Połączono z MongoDB");

    const db = client.db();
    const domainsCollection = db.collection("domains");

    console.log("\n🔍 Sprawdzanie duplikatów domen...");
    
    // Znajdź duplikaty domen (ta sama fullDomain dla tego samego userId)
    const duplicates = await domainsCollection.aggregate([
      {
        $group: {
          _id: { fullDomain: "$fullDomain", userId: "$userId" },
          domains: {
            $push: {
              id: "$_id",
              category: "$category",
              status: "$status",
              linkCount: "$linkCount",
              createdAt: "$createdAt"
            }
          },
          count: { $sum: 1 }
        }
      },
      {
        $match: { count: { $gt: 1 } }
      },
      {
        $sort: { count: -1 }
      }
    ]).toArray();

    console.log(`Znaleziono ${duplicates.length} grup duplikatów`);

    if (duplicates.length > 0) {
      console.log("\n📋 Wszystkie duplikaty:");
      
      // Grupuj duplikaty według domeny
      const domainGroups = {};
      duplicates.forEach(dup => {
        const domain = dup._id.fullDomain;
        if (!domainGroups[domain]) {
          domainGroups[domain] = [];
        }
        domainGroups[domain].push(dup);
      });

      // Wyświetl duplikaty pogrupowane według domeny
      Object.entries(domainGroups).forEach(([domain, groups], index) => {
        const totalCount = groups.reduce((sum, group) => sum + group.count, 0);
        console.log(`${index + 1}. ${domain} (${totalCount} duplikatów)`);
        
        groups.forEach(group => {
          group.domains.forEach((domainInfo, i) => {
            console.log(`   ${i + 1}. Kategoria: ${domainInfo.category}, Status: ${domainInfo.status}, Linki: ${domainInfo.linkCount}`);
          });
        });
        console.log("");
      });

      // Znajdź konkretnie panoramafirm.pl
      const panoramaGroups = duplicates.filter(dup => 
        dup._id.fullDomain.includes('panoramafirm.pl')
      );

      if (panoramaGroups.length > 0) {
        console.log("\n🎯 Szczegóły dla panoramafirm.pl:");
        panoramaGroups.forEach(group => {
          console.log(`Domena: ${group._id.fullDomain}`);
          console.log(`Użytkownik: ${group._id.userId}`);
          console.log(`Liczba duplikatów: ${group.count}`);
          console.log("Kategorie:");
          group.domains.forEach((domain, i) => {
            console.log(`  ${i + 1}. ${domain.category} (Status: ${domain.status}, Linki: ${domain.linkCount})`);
          });
          console.log("");
        });
      }

      // Statystyki
      const totalDuplicateDomains = duplicates.reduce((sum, group) => sum + group.count, 0);
      const uniqueDomainsAffected = duplicates.length;
      
      console.log("\n📊 Statystyki duplikatów:");
      console.log(`- Łączna liczba duplikatów: ${totalDuplicateDomains}`);
      console.log(`- Unikalne domeny z duplikatami: ${uniqueDomainsAffected}`);
      console.log(`- Średnia duplikatów na domenę: ${(totalDuplicateDomains / uniqueDomainsAffected).toFixed(2)}`);

      // Analiza kategorii
      const categoryStats = {};
      duplicates.forEach(group => {
        group.domains.forEach(domain => {
          categoryStats[domain.category] = (categoryStats[domain.category] || 0) + 1;
        });
      });

      console.log("\n📈 Kategorie z duplikatami:");
      Object.entries(categoryStats)
        .sort(([,a], [,b]) => b - a)
        .forEach(([category, count]) => {
          console.log(`- ${category}: ${count} duplikatów`);
        });

    } else {
      console.log("✅ Brak duplikatów w bazie danych");
    }

  } catch (error) {
    console.error("❌ Błąd podczas sprawdzania duplikatów:", error);
  } finally {
    await client.close();
    console.log("\nRozłączono z MongoDB");
  }
}

// Sprawdź czy skrypt jest uruchamiany bezpośrednio
if (require.main === module) {
  console.log("🔍 Sprawdzanie duplikatów domen w bazie danych...");
  
  checkDuplicates().catch(error => {
    console.error("Błąd:", error);
    process.exit(1);
  });
}

module.exports = { checkDuplicates };
