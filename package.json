{"name": "ai-studio", "version": "0.1.0", "private": true, "scripts": {"dev": "concurrently \"next dev --turbopack\" \"N8N_SECURE_COOKIE=false N8N_HOST=0.0.0.0 npx n8n\" \"php -S 0.0.0.0:8000 '/home/<USER>/Projects/ai-studio/scan/audits/index.php'\"", "build": "next build", "start": "concurrently \"next start\" \"N8N_SECURE_COOKIE=false N8N_HOST=0.0.0.0 npx n8n &\"", "lint": "next lint"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.8", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/typography": "^0.5.16", "@types/bcryptjs": "^2.4.6", "@types/express": "^5.0.3", "@types/google.maps": "^3.58.1", "@types/jsonwebtoken": "^9.0.10", "@types/nodemailer": "^6.4.17", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "cheerio": "^1.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "mongodb": "^6.18.0", "mongoose": "^8.15.1", "next": "15.3.3", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "nodemailer": "^6.10.1", "nodemailer-mail-tracking": "^0.7.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.3", "remark-gfm": "^4.0.1", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "zod": "^3.25.42"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.2.0", "eslint": "9.31.0", "eslint-config-next": "15.4.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.2", "typescript": "^5"}}