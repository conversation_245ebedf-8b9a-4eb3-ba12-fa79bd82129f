"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import { FileText, Loader2, AlertCircle, RefreshCw } from "lucide-react";

interface AuditData {
  date: string;
  label: string;
  audits: number;
}

interface AuditStatsResponse {
  success: boolean;
  data: AuditData[];
  summary: {
    totalAudits: number;
    period: string;
    startDate: string;
    endDate: string;
  };
  debug?: {
    totalDomains: number;
    domainsWithAudits: number;
    userId: string;
    aggregationResults: number;
  };
}

interface CustomTooltipProps {
  active?: boolean;
  payload?: Array<{
    value: number;
    payload: AuditData;
  }>;
  label?: string;
}

const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    // Formatuj pełną datę dla tooltipa
    const date = new Date(data.date);
    const fullDate = date.toLocaleDateString('pl-PL', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    return (
      <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
        <p className="font-medium text-foreground">{fullDate}</p>
        <p className="text-primary">
          <span className="font-semibold">{payload[0].value}</span> audytów
        </p>
      </div>
    );
  }
  return null;
};

export default function AuditChart() {
  const [data, setData] = useState<AuditData[]>([]);
  const [summary, setSummary] = useState<AuditStatsResponse['summary'] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const fetchAuditStats = async (isRefresh = false) => {
    if (isRefresh) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }
    setError(null);

    try {
      const response = await fetch("/api/dashboard/audit-stats", {
        cache: 'no-cache', // Ensure fresh data
        headers: {
          'Cache-Control': 'no-cache'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: AuditStatsResponse = await response.json();

      if (result.success) {
        console.log('🔍 AuditChart - Received data:', {
          totalAudits: result.summary?.totalAudits,
          dataLength: result.data?.length,
          debug: result.debug,
          july20Data: result.data?.find(d => d.date === '2025-07-20'),
          sampleData: result.data?.filter(d => d.audits > 0).slice(0, 5)
        });
        setData(result.data);
        setSummary(result.summary);
      } else {
        throw new Error("Failed to fetch audit statistics");
      }
    } catch (e) {
      console.error("Failed to fetch audit stats:", e);
      setError("Nie udało się załadować statystyk audytów. Spróbuj ponownie później.");
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchAuditStats();
  }, []);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Audyty z ostatnich 30 dni
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="ml-2 text-muted-foreground">Ładowanie danych...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Audyty z ostatnich 30 dni
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8 text-destructive">
            <AlertCircle className="h-8 w-8 mr-2" />
            <p>{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Audyty z ostatnich 30 dni
          </span>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchAuditStats(true)}
              disabled={refreshing}
              className="h-8"
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
            {summary && (
              <span className="text-2xl font-bold text-primary">
                {summary.totalAudits}
              </span>
            )}
          </div>
        </CardTitle>
        {summary && (
          <p className="text-sm text-muted-foreground">
            Łącznie {summary.totalAudits} audytów przeprowadzonych w ciągu ostatnich {summary.period}
          </p>
        )}
      </CardHeader>
      <CardContent>
        <div className="h-[400px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={data}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis
                dataKey="label"
                tick={{ fontSize: 10 }}
                angle={-45}
                textAnchor="end"
                height={80}
                interval="preserveStartEnd"
              />
              <YAxis
                tick={{ fontSize: 12 }}
                allowDecimals={false}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar
                dataKey="audits"
                fill="hsl(var(--chart-1))"
                radius={[4, 4, 0, 0]}
                className="hover:opacity-80 transition-opacity"
              />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {data.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Brak audytów z ostatnich 30 dni</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
