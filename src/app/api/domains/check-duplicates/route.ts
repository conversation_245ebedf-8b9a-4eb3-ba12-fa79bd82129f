import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Domain, { IDomain } from '@/models/Domain';
import { PipelineStage } from 'mongoose'; // Assuming mongoose exports PipelineStage, or it's from 'mongodb'

interface Session {
  user: {
    id: string;
    email: string;
    name: string;
  };
}

interface AggregatedDomainInfo {
  id: string;
  category: string;
  status: string;
  linkCount: number;
  createdAt: Date;
  updatedAt: Date;
}

interface DuplicateGroupGet {
  fullDomain: string;
  userId: string;
  domains: AggregatedDomainInfo[];
  totalCount: number;
}

interface DuplicateGroupPost {
  _id: string; // fullDomain
  domains: AggregatedDomainInfo[];
  count: number;
}


export async function GET(request: NextRequest) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { searchParams } = new URL(request.url);
    const showAll = searchParams.get('all') === 'true'; // Parametr do pokazania wszystkich użytkowników (admin)

    // Agregacja do znalezienia duplikatów domen
    const pipeline: PipelineStage[] = [
      // Filtruj po użytkowniku jeśli nie jest admin
      ...(showAll ? [] : [{ $match: { userId: session.user.id } }]),

      // Grupuj po fullDomain i userId
      {
        $group: {
          _id: { fullDomain: '$fullDomain', userId: '$userId' },
          domains: {
            $push: {
              id: '$_id',
              category: '$category',
              status: '$status',
              linkCount: '$linkCount',
              createdAt: '$createdAt',
              updatedAt: '$updatedAt'
            }
          },
          count: { $sum: 1 }
        }
      },

      // Filtruj tylko te grupy, które mają więcej niż 1 domenę (duplikaty)
      {
        $match: { count: { $gt: 1 } }
      },

      // Sortuj po liczbie duplikatów (malejąco)
      {
        $sort: { count: -1 as const } // Explicitly cast -1 as a literal type
      },

      // Przekształć wyniki
      {
        $project: {
          fullDomain: '$_id.fullDomain',
          userId: '$_id.userId',
          domains: '$domains',
          totalCount: '$count'
        }
      }
    ];

    const duplicates = await Domain.aggregate(pipeline);

    // Oblicz statystyki
    const totalDuplicateGroups = duplicates.length;
    const totalDuplicateDomains = duplicates.reduce((sum, group) => sum + group.totalCount, 0);
    const totalUniqueDomainsAffected = duplicates.length;

    // Znajdź najczęstsze duplikaty
    const topDuplicates = duplicates.slice(0, 10);

    // Analiza kategorii z duplikatami
    const categoryAnalysis: Record<string, number> = {};
    duplicates.forEach(group => {
      group.domains.forEach((domain: IDomain) => {
        categoryAnalysis[domain.category] = (categoryAnalysis[domain.category] || 0) + 1;
      });
    });

    const response = {
      success: true,
      summary: {
        totalDuplicateGroups,
        totalDuplicateDomains,
        totalUniqueDomainsAffected,
        averageDuplicatesPerDomain: totalDuplicateGroups > 0 ?
          (totalDuplicateDomains / totalDuplicateGroups).toFixed(2) : 0
      },
      duplicates: duplicates.map((group: DuplicateGroupGet) => ({
        fullDomain: group.fullDomain,
        userId: group.userId,
        totalCount: group.totalCount,
        categories: group.domains.map((d: AggregatedDomainInfo) => d.category),
        domains: group.domains
      })),
      topDuplicates: topDuplicates.map((group: DuplicateGroupGet) => ({
        fullDomain: group.fullDomain,
        count: group.totalCount,
        categories: [...new Set(group.domains.map((d: AggregatedDomainInfo) => d.category))]
      })),
      categoryAnalysis: Object.entries(categoryAnalysis)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([category, count]) => ({ category, count }))
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Błąd sprawdzania duplikatów:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas sprawdzania duplikatów' },
      { status: 500 }
    );
  }
}

// POST endpoint do usuwania duplikatów
export async function POST(request: NextRequest) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { strategy, dryRun = true } = await request.json();

    if (!['keep-oldest', 'keep-newest', 'keep-highest-links'].includes(strategy)) {
      return NextResponse.json(
        { error: 'Nieprawidłowa strategia. Dostępne: keep-oldest, keep-newest, keep-highest-links' },
        { status: 400 }
      );
    }

    // Znajdź duplikaty dla użytkownika
    const duplicates = await Domain.aggregate([
      { $match: { userId: session.user.id } },
      {
        $group: {
          _id: '$fullDomain',
          domains: {
            $push: {
              id: '$_id',
              category: '$category',
              status: '$status',
              linkCount: '$linkCount',
              createdAt: '$createdAt',
              updatedAt: '$updatedAt'
            }
          },
          count: { $sum: 1 }
        }
      },
      { $match: { count: { $gt: 1 } } }
    ]);

    const toDelete: string[] = [];
    const toKeep: string[] = [];

    duplicates.forEach((group: DuplicateGroupPost) => {
      const domains = group.domains;
      let domainToKeep: AggregatedDomainInfo = domains[0]; // Initialize with the first domain

      switch (strategy) {
        case 'keep-oldest':
          domainToKeep = domains.reduce((oldest: AggregatedDomainInfo, current: AggregatedDomainInfo) =>
            new Date(current.createdAt) < new Date(oldest.createdAt) ? current : oldest
          );
          break;
        case 'keep-newest':
          domainToKeep = domains.reduce((newest: AggregatedDomainInfo, current: AggregatedDomainInfo) =>
            new Date(current.createdAt) > new Date(newest.createdAt) ? current : newest
          );
          break;
        case 'keep-highest-links':
          domainToKeep = domains.reduce((highest: AggregatedDomainInfo, current: AggregatedDomainInfo) =>
            current.linkCount > highest.linkCount ? current : highest
          );
          break;
      }

      toKeep.push(domainToKeep.id);
      domains.forEach((domain: AggregatedDomainInfo) => {
        if (domain.id !== domainToKeep.id) {
          toDelete.push(domain.id);
        }
      });
    });

    let deletedCount = 0;
    if (!dryRun && toDelete.length > 0) {
      const result = await Domain.deleteMany({
        _id: { $in: toDelete },
        userId: session.user.id
      });
      deletedCount = result.deletedCount;
    }

    return NextResponse.json({
      success: true,
      dryRun,
      strategy,
      summary: {
        duplicateGroups: duplicates.length,
        domainsToDelete: toDelete.length,
        domainsToKeep: toKeep.length,
        actuallyDeleted: deletedCount
      },
      toDelete: dryRun ? toDelete : [],
      toKeep: dryRun ? toKeep : []
    });

  } catch (error) {
    console.error('Błąd usuwania duplikatów:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas usuwania duplikatów' },
      { status: 500 }
    );
  }
}
