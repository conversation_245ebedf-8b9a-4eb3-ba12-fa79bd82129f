import { NextResponse } from 'next/server'; // Removed NextRequest
import { getServerSession } from 'next-auth/next'; // Changed import path
import { Session } from 'next-auth'; // Added Session import
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Link from '@/models/Link';
import Domain from '@/models/Domain';
import { checkDomainCms } from '@/lib/cms-checker';

interface DomainInfo {
  protocol: string;
  domain: string;
  fullDomain: string;
  category: string;
  linkCount: number;
}

interface CreateDomainsResult {
  domain: string;
  fullDomain: string;
  category: string;
  linkCount: number;
  cms: string;
  status: string;
  error?: string;
}

export async function POST() {
  try {
    const session = (await getServerSession(authOptions)) as Session | null; // Explicitly cast session

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    // Pobierz wszystkie linki użytkownika
    const links = await Link.find({ userId: session.user.id });

    if (links.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'Brak linków do przetworzenia',
        processed: 0,
        created: 0,
        skipped: 0,
        results: [],
        summary: { wordpress: 0, inny: 0 }
      });
    }

    // Grupuj linki po domenach i kategoriach
    const domainMap = new Map<string, DomainInfo>();

    for (const link of links) {
      try {
        const url = new URL(link.url);
        const protocol = url.protocol.replace(':', '');
        const domain = url.hostname;
        const fullDomain = `${protocol}://${domain}`;
        const category = link.category;

        // Klucz unikalny: fullDomain + category
        const key = `${fullDomain}|${category}`;

        if (domainMap.has(key)) {
          // Zwiększ licznik linków dla tej domeny w tej kategorii
          domainMap.get(key)!.linkCount++;
        } else {
          // Dodaj nową domenę
          domainMap.set(key, {
            protocol,
            domain,
            fullDomain,
            category,
            linkCount: 1
          });
        }
      } catch (error) {
        console.error('Błąd parsowania URL:', link.url, error);
        // Pomiń nieprawidłowe URL-e
        continue;
      }
    }

    // Sprawdź które domeny już istnieją w bazie
    const results: CreateDomainsResult[] = [];
    let created = 0;
    let skipped = 0;
    let wordpressCount = 0;
    let innyCount = 0;

    for (const [key, domainInfo] of domainMap) {
      console.log(key)
      try {
        // Sprawdź czy domena już istnieje dla tego użytkownika (globalnie)
        const existingDomain = await Domain.findOne({
          userId: session.user.id,
          fullDomain: domainInfo.fullDomain
        });

        if (existingDomain) {
          // Domena już istnieje - aktualizuj tylko linkCount jeśli jest większy
          if (domainInfo.linkCount > existingDomain.linkCount) {
            existingDomain.linkCount = domainInfo.linkCount;
            await existingDomain.save();
          }

          results.push({
            domain: domainInfo.domain,
            fullDomain: domainInfo.fullDomain,
            category: domainInfo.category,
            linkCount: domainInfo.linkCount,
            cms: existingDomain.cms || 'nieznany',
            status: 'skipped',
            error: `Domena już istnieje w kategorii "${existingDomain.category}"`
          });
          skipped++;
          continue;
        }

        // Sprawdź CMS dla nowej domeny
        const cmsCheckResult = await checkDomainCms(domainInfo.fullDomain);
        const cmsType = cmsCheckResult.cms;

        // Utwórz nową domenę
        const newDomain = new Domain({
          domain: domainInfo.domain,
          protocol: domainInfo.protocol,
          fullDomain: domainInfo.fullDomain,
          category: domainInfo.category,
          userId: session.user.id,
          linkCount: domainInfo.linkCount,
          cms: cmsType
        });

        await newDomain.save();

        results.push({
          domain: domainInfo.domain,
          fullDomain: domainInfo.fullDomain,
          category: domainInfo.category,
          linkCount: domainInfo.linkCount,
          cms: cmsType,
          status: 'created'
        });

        created++;
        if (cmsType === 'wordpress') {
          wordpressCount++;
        } else {
          innyCount++;
        }

      } catch (error) {
        console.error('Błąd tworzenia domeny:', domainInfo.fullDomain, error);
        results.push({
          domain: domainInfo.domain,
          fullDomain: domainInfo.fullDomain,
          category: domainInfo.category,
          linkCount: domainInfo.linkCount,
          cms: 'nieznany',
          status: 'error',
          error: error instanceof Error ? error.message : 'Nieznany błąd'
        });
        skipped++;
      }
    }

    return NextResponse.json({
      success: true,
      message: `Przetworzono ${domainMap.size} unikalnych domen. Utworzono: ${created}, pominięto: ${skipped}`,
      processed: domainMap.size,
      created,
      skipped,
      results,
      summary: {
        wordpress: wordpressCount,
        inny: innyCount
      }
    });

  } catch (error) {
    console.error('Błąd tworzenia domen z linków:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas tworzenia domen z linków' },
      { status: 500 }
    );
  }
}
