import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import mongoose from 'mongoose';
import Domain, { IDomain } from '@/models/Domain';
import { sendAcceptedDomainToWebhook } from '@/lib/webhook';

export async function GET() {
  try {
    await connectDB();

    // Fetch one domain with status 'in_audit'
    const acceptedDomain: IDomain | null = await Domain.findOne({
      status: "in_audit"
    }).lean<IDomain>(); // .lean() returns a plain JavaScript object, not a Mongoose document

    if (!acceptedDomain) {
      return NextResponse.json(
        { message: 'No accepted domains found.' },
        { status: 404 }
      );
    }

    // Prepare the data in the desired format, similar to domain details
    const domainData = {
      id: (acceptedDomain._id as mongoose.Types.ObjectId).toString(), // Convert ObjectId to string
      domain: acceptedDomain.domain,
      protocol: acceptedDomain.protocol,
      fullDomain: acceptedDomain.fullDomain,
      category: acceptedDomain.category,
      userId: acceptedDomain.userId, // userId is still included in the response
      linkCount: acceptedDomain.linkCount,
      status: acceptedDomain.status,
      cms: acceptedDomain.cms,
      auditContent: acceptedDomain.auditContent,
      lighthouseContent: acceptedDomain.lighthouseContent,
      contact_data: acceptedDomain.contact_data,
      metadata: acceptedDomain.metadata,
      createdAt: acceptedDomain.createdAt,
      updatedAt: acceptedDomain.updatedAt,
    };

    return NextResponse.json(domainData);

  } catch (error) {
    console.error("[GET_NEXT_IN_AUDIT_DOMAIN]", error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: 'An error occurred while fetching the domain.', details: errorMessage },
      { status: 500 }
    );
  }
}

// PATCH endpoint to update domain status or other properties (without authorization)
export async function PATCH(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const { id, status, ...otherUpdates } = body;

    // Validate required fields
    if (!id) {
      return NextResponse.json(
        { error: 'Domain ID is required' },
        { status: 400 }
      );
    }

    // Validate status if provided
    if (status && !['new', 'accepted', 'rejected', 'closed', 'in_progress', 'in_audit', 'production', 'hold'].includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status. Must be one of: new, accepted, rejected, closed, in_progress, in_audit, production, hold' },
        { status: 400 }
      );
    }

    // Find the domain (without user restriction since this is a webhook)
    const domain = await Domain.findById(id);

    if (!domain) {
      return NextResponse.json(
        { error: 'Domain not found' },
        { status: 404 }
      );
    }

    // Prepare update object
    const updateObject: Record<string, object> = {
      updatedAt: new Date()
    };

    if (status) {
      updateObject.status = status;
    }

    // Add any other updates from the request body
    Object.keys(otherUpdates).forEach(key => {
      if (otherUpdates[key] !== undefined) {
        updateObject[key] = otherUpdates[key];
      }
    });

    // Update the domain
    const updatedDomain = await Domain.findByIdAndUpdate(
      id,
      { $set: updateObject },
      { new: true }
    ).lean<IDomain>();

    if (!updatedDomain) {
      return NextResponse.json(
        { error: 'Failed to update domain' },
        { status: 500 }
      );
    }

    // Jeśli domena została zaakceptowana, wyślij dane na webhook i pobierz użytkowników WordPress
    if (updatedDomain) {
      try {
        await sendAcceptedDomainToWebhook(updatedDomain);

        // Pobierz zaktualizowaną domenę z bazy danych po wywołaniu webhook
        // (funkcja sendAcceptedDomainToWebhook może zaktualizować metadata)
        const refreshedDomain = await Domain.findById(id).lean<IDomain>();
        if (refreshedDomain) {
          updatedDomain.metadata = refreshedDomain.metadata;
          updatedDomain.updatedAt = refreshedDomain.updatedAt;
        }
      } catch (webhookError) {
        console.error('Błąd wysyłania na webhook:', webhookError);
        // Nie przerywamy procesu - domena została zaakceptowana, ale webhook się nie udał
      }
    }

    // Prepare the response data
    const domainData = {
      id: (updatedDomain._id as mongoose.Types.ObjectId).toString(),
      domain: updatedDomain.domain,
      protocol: updatedDomain.protocol,
      fullDomain: updatedDomain.fullDomain,
      category: updatedDomain.category,
      userId: updatedDomain.userId,
      linkCount: updatedDomain.linkCount,
      status: updatedDomain.status,
      cms: updatedDomain.cms,
      auditContent: updatedDomain.auditContent,
      lighthouseContent: updatedDomain.lighthouseContent,
      contact_data: updatedDomain.contact_data,
      metadata: updatedDomain.metadata,
      createdAt: updatedDomain.createdAt,
      updatedAt: updatedDomain.updatedAt,
    };

    return NextResponse.json({
      success: true,
      message: 'Domain updated successfully',
      domain: domainData
    });

  } catch (error) {
    console.error("[PATCH_DOMAIN_WEBHOOK]", error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: 'An error occurred while updating the domain.', details: errorMessage },
      { status: 500 }
    );
  }
}
