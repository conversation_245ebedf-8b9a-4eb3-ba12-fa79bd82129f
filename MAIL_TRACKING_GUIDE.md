# Przewodnik po systemie śledzenia maili

## Przegląd

System śledzenia maili w AI Studio umożliwia monitorowanie wysyłek, otwarć i kliknięć w mailach audytowych. Wykorzystuje bibliotekę `nodemailer-mail-tracking` do automatycznego dodawania pixeli trackingu i modyfikowania linków.

## Funkcjonalności

### 🎯 Automatyczne śledzenie
- **Pixel tracking** - niewidoczny pixel do śledzenia otwarć
- **Link tracking** - automatyczne przekierowania przez system trackingu
- **Event logging** - zapisywanie wszystkich eventów w bazie danych

### 📊 Typy eventów
- `sent` - mail został wysłany
- `opened` - mail został otwarty (załadowanie pixela)
- `clicked` - kliknięcie w link w mailu
- `delivered` - mail został dostarczony (przyszłe rozszerzenie)
- `bounced` - mail został odrzucony (przyszłe rozszerzenie)
- `complained` - zgłoszenie spamu (przyszłe rozszerzenie)

### 📈 Dashboard śledzenia
- Statystyki: wysłane maile, wskaźnik otwarć, wskaźnik kliknięć
- Lista wszystkich eventów z filtrowaniem
- Szczegóły eventów: data, typ, odbiorca, temat, dodatkowe dane

## Jak to działa

### 1. Wysyłanie maila z trackingiem
```
Użytkownik → Wysyła mail audytowy → System dodaje tracking → Mail z pixelem i zmodyfikowanymi linkami
```

### 2. Śledzenie otwarcia
```
Odbiorca otwiera mail → Ładuje się pixel → GET /api/mail-tracking/blank-image/[jwt] → Event 'opened' w bazie
```

### 3. Śledzenie kliknięć
```
Odbiorca klika link → Przekierowanie przez /api/mail-tracking/link/[jwt] → Event 'clicked' → Przekierowanie do oryginalnego linku
```

## Instrukcja użytkowania

### Krok 1: Konfiguracja SMTP
Upewnij się, że masz skonfigurowane zmienne środowiskowe w `.env`:

```env
SMTP_HOST=your-smtp-host.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-password
SMTP_FROM=<EMAIL>
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key
```

### Krok 2: Wysyłanie maili audytowych
1. Przejdź do szczegółów domeny: `/dashboard/domains/[id]/audit`
2. Kliknij "Wyślij email z audytem"
3. Wypełnij formularz z adresem odbiorcy i tematem
4. Kliknij "Wyślij email"

### Krok 3: Monitorowanie trackingu
1. Przejdź do dashboard śledzenia: `/dashboard/email-tracking`
2. Sprawdź statystyki w kartach na górze
3. Przeglądaj listę eventów w tabeli
4. Używaj filtrów do zawężenia wyników

### Krok 4: Analiza wyników
- **Wskaźnik otwarć** - procent maili, które zostały otwarte
- **Wskaźnik kliknięć** - procent maili, w których kliknięto linki
- **Szczegóły eventów** - data, typ, odbiorca, dodatkowe informacje

## API Endpoints

### Publiczne (tracking)
- `GET /api/mail-tracking/blank-image/[jwt]` - Pixel trackingu otwarć
- `GET /api/mail-tracking/link/[jwt]` - Przekierowania z trackingiem

### Chronione (dashboard)
- `GET /api/email-tracking` - Lista eventów trackingu
- `GET /api/email-tracking/stats` - Statystyki trackingu

## Bezpieczeństwo

### JWT Tokeny
- Ważność: 1 rok
- Zawartość: tylko niezbędne dane (recipient, link)
- Podpisane kluczem NEXTAUTH_SECRET

### Walidacja
- Wszystkie tokeny są walidowane przy każdym requeście
- Graceful handling błędnych tokenów
- Brak wrażliwych danych w tokenach

### Metadane
- User-Agent przeglądarki
- Adres IP (z proxy headers)
- Timestamp eventu
- Źródło eventu

## Rozwiązywanie problemów

### Problem: Maile nie są śledzone
**Rozwiązanie:**
1. Sprawdź konfigurację SMTP
2. Upewnij się, że NEXTAUTH_URL jest poprawne
3. Sprawdź logi aplikacji w konsoli

### Problem: Pixel tracking nie działa
**Rozwiązanie:**
1. Sprawdź czy NEXTAUTH_SECRET jest ustawione
2. Sprawdź czy endpoint `/api/mail-tracking/blank-image/test` zwraca SVG
3. Sprawdź blokery reklam u odbiorcy

### Problem: Link tracking nie działa
**Rozwiązanie:**
1. Sprawdź czy linki w mailu są modyfikowane
2. Sprawdź czy JWT tokeny są poprawnie generowane
3. Sprawdź logi błędów w konsoli przeglądarki

### Problem: Brak danych w dashboard
**Rozwiązanie:**
1. Sprawdź połączenie z MongoDB
2. Sprawdź czy eventy są zapisywane w bazie
3. Sprawdź autoryzację użytkownika

## Testowanie

### Test automatyczny
```bash
node test_mail_tracking.js
```

### Test manualny
1. Wyślij mail testowy do siebie
2. Otwórz mail w kliencie pocztowym
3. Kliknij link w mailu
4. Sprawdź dashboard śledzenia

## Przyszłe rozszerzenia

### Planowane funkcjonalności
- Śledzenie dostarczeń (delivered)
- Śledzenie odrzuceń (bounced)
- Śledzenie zgłoszeń spamu (complained)
- Eksport danych do CSV/Excel
- Powiadomienia o eventach
- Integracja z zewnętrznymi serwisami

### Możliwe ulepszenia
- Geolokalizacja na podstawie IP
- Analiza urządzeń i przeglądarek
- A/B testing maili
- Segmentacja odbiorców
- Automatyczne follow-up maile

## Wsparcie

W przypadku problemów:
1. Sprawdź logi aplikacji
2. Uruchom test automatyczny
3. Sprawdź konfigurację środowiska
4. Skontaktuj się z administratorem systemu

## Changelog

### v1.0.0 (2025-01-21)
- ✅ Podstawowe śledzenie otwarć i kliknięć
- ✅ Dashboard z statystykami
- ✅ API endpoints
- ✅ Integracja z nodemailer-mail-tracking
- ✅ Bezpieczne tokeny JWT
- ✅ Dokumentacja i testy
