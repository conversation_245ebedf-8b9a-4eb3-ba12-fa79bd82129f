import nodemailer from 'nodemailer';
import { sendTrackedEmail } from '@/lib/mail-tracking';

// SMTP configuration interface
interface SMTPConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

// Email options interface
interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

// Get SMTP configuration from environment variables
function getSMTPConfig(): SMTPConfig {
  const host = process.env.SMTP_HOST;
  const port = process.env.SMTP_PORT;
  const user = process.env.SMTP_USER;
  const pass = process.env.SMTP_PASS;

  if (!host || !port || !user || !pass) {
    throw new Error('SMTP configuration is incomplete. Please check environment variables: SMTP_HOST, SMTP_PORT, SMTP_USER, SMTP_PASS');
  }

  return {
    host,
    port: parseInt(port),
    secure: parseInt(port) === 465, // true for 465, false for other ports
    auth: {
      user,
      pass,
    },
  };
}

// Create transporter with SMTP configuration
function createTransporter() {
  const config = getSMTPConfig();
  return nodemailer.createTransport(config);
}

// Send email function
export async function sendEmail(options: EmailOptions): Promise<boolean> {
  try {
    const transporter = createTransporter();

    // Verify SMTP connection
    await transporter.verify();

    // Send email
    const info = await transporter.sendMail({
      from: process.env.SMTP_FROM || process.env.SMTP_USER,
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text || options.html.replace(/<[^>]*>/g, ''), // Strip HTML tags for text version
    });

    console.log('Email sent successfully:', info.messageId);
    return true;
  } catch (error) {
    console.error('Error sending email:', error);
    return false;
  }
}

// Helper function to decode JSON string if needed
function decodeAuditContent(content: string): string {
  if (!content) return "";

  try {
    // Try to parse as JSON - if it's a JSON string, decode it
    const parsed = JSON.parse(content);
    if (typeof parsed === "string") {
      return parsed;
    }
    // If it's not a string after parsing, return original
    return content;
  } catch (error) {
    console.log(error)
    // If JSON.parse fails, it's not a JSON string, return as is
    return content;
  }
}

// Generate HTML content for audit email
export function generateAuditEmailHTML(
  auditContent: string,
  domainName: string
): string {
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Audyt Bezpieczeństwa - ${domainName}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 800px;
          margin: 0;
          padding: 20px;
        }

        .content {
          background-color: #ffffff;
          padding: 20px;
          border-radius: 8px;
        }

        pre {
          background-color: #f8f9fa;
          padding: 15px;
          border-radius: 4px;
          overflow-x: auto;
          white-space: pre-wrap;
          word-wrap: break-word;
        }
      </style>
    </head>
    <body>

      <div class="content">
        <div style="width: 960px; max-width: 100%;">
          ${decodeAuditContent(auditContent)}
        </div>
      </div>
    </body>
    </html>
  `;

  return htmlContent;
}

// Send audit email specifically with tracking
export async function sendAuditEmail(
  recipientEmail: string,
  subject: string,
  auditContent: string,
  domainName: string,
  emailHistoryId?: string
): Promise<{ success: boolean; htmlContent: string }> {
  const htmlContent = generateAuditEmailHTML(auditContent, domainName);

  try {
    // Użyj trackowanego wysyłania maili
    const results = await sendTrackedEmail(
      recipientEmail,
      subject,
      htmlContent,
      emailHistoryId
    );

    // Sprawdź czy wysyłanie się powiodło
    const success = results.every(result => !result.error);

    return {
      success,
      htmlContent: htmlContent
    };
  } catch (error) {
    console.error('Error sending tracked audit email:', error);

    // Fallback do standardowego wysyłania
    const emailSent = await sendEmail({
      to: recipientEmail,
      subject,
      html: htmlContent,
    });

    return {
      success: emailSent,
      htmlContent: htmlContent
    };
  }
}

// Test SMTP connection
export async function testSMTPConnection(): Promise<boolean> {
  try {
    const transporter = createTransporter();
    await transporter.verify();
    console.log('SMTP connection test successful');
    return true;
  } catch (error) {
    console.error('SMTP connection test failed:', error);
    return false;
  }
}
