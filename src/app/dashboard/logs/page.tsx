"use client";

import { useEffect, useState, useCallback } from "react";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  AlertTriangle,
  AlertCircle,
  Info,
  Bug,
  Loader2,
  ChevronLeft,
  ChevronRight,
  Filter,
  Trash2,
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  <PERSON><PERSON><PERSON><PERSON>ogHead<PERSON>,
  AlertDialogTitle,
  Alert<PERSON><PERSON>ogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { format } from "date-fns";
import { pl } from "date-fns/locale";

interface LogEntry {
  _id: string;
  level: "info" | "warn" | "error" | "debug";
  message: string;
  source?: string;
  userId?: string;
  metadata?: Record<string, string | number | boolean | null>;
  createdAt: string;
  updatedAt: string;
}

interface LogsResponse {
  success: boolean;
  logs: LogEntry[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

const levelIcons = {
  info: Info,
  warn: AlertTriangle,
  error: AlertCircle,
  debug: Bug,
};

const levelColors = {
  info: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  warn: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  error: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
  debug: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
};

export default function LogsPage() {
  const { data: session } = useSession();
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    totalCount: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });

  // Filters
  const [levelFilter, setLevelFilter] = useState<string>("all");
  const [sourceFilter, setSourceFilter] = useState<string>("");
  const [showFilters, setShowFilters] = useState(false);

  // Clear logs state
  const [showClearDialog, setShowClearDialog] = useState(false);
  const [isClearing, setIsClearing] = useState(false);

  const fetchLogs = useCallback(async (page: number = 1) => {
    if (!session) return;

    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
      });

      if (levelFilter && levelFilter !== "all")
        params.append("level", levelFilter);
      if (sourceFilter) params.append("source", sourceFilter);

      const response = await fetch(`/api/logs?${params}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: LogsResponse = await response.json();

      if (data.success) {
        setLogs(data.logs);
        setPagination(data.pagination);
      } else {
        throw new Error("Failed to fetch logs");
      }
    } catch (e) {
      console.error("Failed to fetch logs:", e);
      setError("Nie udało się załadować logów. Spróbuj ponownie później.");
    }

    setLoading(false);
  }, [session, pagination.limit, levelFilter, sourceFilter]);

  useEffect(() => {
    if (session) {
      fetchLogs(1);
    }
  }, [session, fetchLogs]);

  const handlePageChange = (newPage: number) => {
    fetchLogs(newPage);
  };

  const clearFilters = () => {
    setLevelFilter("all");
    setSourceFilter("");
  };

  const handleClearAllLogs = async () => {
    if (!session) return;

    setIsClearing(true);
    try {
      const response = await fetch("/api/logs", {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        toast.success(data.message);
        // Refresh logs after clearing
        fetchLogs(1);
      } else {
        throw new Error(data.error || "Failed to clear logs");
      }
    } catch (e) {
      console.error("Failed to clear logs:", e);
      toast.error("Nie udało się wyczyścić logów. Spróbuj ponownie później.");
    } finally {
      setIsClearing(false);
      setShowClearDialog(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Logi Systemowe</h1>
          <p className="text-muted-foreground">
            Przeglądaj logi systemowe i błędy aplikacji.
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            {showFilters ? "Ukryj filtry" : "Pokaż filtry"}
          </Button>

          <AlertDialog open={showClearDialog} onOpenChange={setShowClearDialog}>
            <AlertDialogTrigger asChild>
              <Button
                variant="destructive"
                className="flex items-center gap-2"
                disabled={pagination.totalCount === 0}
              >
                <Trash2 className="h-4 w-4" />
                Wyczyść wszystkie logi
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Wyczyść wszystkie logi</AlertDialogTitle>
                <AlertDialogDescription>
                  Czy na pewno chcesz usunąć wszystkie logi systemowe?
                  <br />
                  <br />
                  Ta akcja jest nieodwracalna i usunie wszystkie {pagination.totalCount} wpisów logów.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Anuluj</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleClearAllLogs}
                  disabled={isClearing}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  {isClearing ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Czyszczenie...
                    </>
                  ) : (
                    "Wyczyść wszystkie logi"
                  )}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filtry</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Poziom</label>
                <Select value={levelFilter} onValueChange={setLevelFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Wszystkie poziomy" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Wszystkie poziomy</SelectItem>
                    <SelectItem value="info">Info</SelectItem>
                    <SelectItem value="warn">Ostrzeżenie</SelectItem>
                    <SelectItem value="error">Błąd</SelectItem>
                    <SelectItem value="debug">Debug</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Źródło</label>
                <Input
                  placeholder="Filtruj po źródle..."
                  value={sourceFilter}
                  onChange={(e) => setSourceFilter(e.target.value)}
                />
              </div>

              <div className="flex items-end">
                <Button variant="outline" onClick={clearFilters}>
                  Wyczyść filtry
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Logs Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Logi ({pagination.totalCount})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading && (
            <div className="flex items-center justify-center py-10">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <p className="ml-2 text-muted-foreground">Ładowanie logów...</p>
            </div>
          )}

          {error && (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
              <p className="text-destructive">{error}</p>
              <Button
                variant="outline"
                onClick={() => fetchLogs(pagination.page)}
                className="mt-4"
              >
                Spróbuj ponownie
              </Button>
            </div>
          )}

          {!loading && !error && logs.length === 0 && (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                Brak logów do wyświetlenia.
              </p>
            </div>
          )}

          {!loading && !error && logs.length > 0 && (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Poziom</TableHead>
                    <TableHead>Wiadomość</TableHead>
                    <TableHead>Źródło</TableHead>
                    <TableHead>Data</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {logs.map((log) => {
                    const LevelIcon = levelIcons[log.level] || Info;
                    return (
                      <TableRow key={log._id}>
                        <TableCell>
                          <Badge
                            className={
                              levelColors[log.level] || levelColors.info
                            }
                          >
                            <LevelIcon className="h-3 w-3 mr-1" />
                            {log.level?.toUpperCase() || "INFO"}
                          </Badge>
                        </TableCell>
                        <TableCell className="max-w-md">
                          <div
                            className="truncate"
                            title={log.message}
                            dangerouslySetInnerHTML={{ __html: log.message }}
                          />
                        </TableCell>
                        <TableCell>
                          {log.source && (
                            <Badge variant="outline">{log.source}</Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {format(
                            new Date(log.createdAt),
                            "dd.MM.yyyy HH:mm:ss",
                            { locale: pl }
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>

              {/* Pagination */}
              {pagination.totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    Strona {pagination.page} z {pagination.totalPages}(
                    {pagination.totalCount} logów)
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={!pagination.hasPrev}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Poprzednia
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={!pagination.hasNext}
                    >
                      Następna
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
