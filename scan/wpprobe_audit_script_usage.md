# WPProbe Audit Script - Instrukcja użycia

## Opis
Skrypt `wpprobe_audit_script.sh` służy do automatycznego audytu domen WordPress przy użyciu narzędzi wpprobe i lighthouse.

## Sk<PERSON><PERSON><PERSON>
```bash
./wpprobe_audit_script.sh [OPCJE] [PARAMETRY_API]
```

## Opcje

### `-t, --test`
Uruchamia skrypt w trybie testowym. W tym trybie:
- Używany jest testowy webhook URL
- Wszystkie dane są wysyłane na endpoint testowy

### `-s, --skip-lighthouse`
Pomija skanowanie Lighthouse. Przydatne gdy:
- Chcemy tylko skanowanie wpprobe
- Lighthouse powoduje problemy
- Chcemy przyspieszyć proces

### `-h, --help`
Wyświetla pomoc i kończy działanie skryptu.

## Parametry API

Skrypt pozwala na przekazywanie dodatkowych parametrów do API endpoint. Parametry są dodawane jako query string do URL.

### Format parametrów
Parametry powinny być przekazane jako pojedynczy string w formacie `klucz=wartość&klucz2=wartość2`.

### Przykłady parametrów
- `limit=10` - ogranicza liczbę zwracanych domen
- `status=pending` - filtruje domeny według statusu
- `priority=high` - filtruje według priorytetu
- `limit=5&status=active` - kombinacja parametrów

## Przykłady użycia

### Podstawowe uruchomienie
```bash
./wpprobe_audit_script.sh
```
Uruchamia skrypt bez dodatkowych parametrów.

### Tryb testowy
```bash
./wpprobe_audit_script.sh --test
```
Uruchamia w trybie testowym z testowym webhook.

### Z parametrami API
```bash
./wpprobe_audit_script.sh 'limit=5&status=pending'
```
Pobiera maksymalnie 5 domen ze statusem "pending".

### Kombinacja opcji
```bash
./wpprobe_audit_script.sh --test 'limit=10' --skip-lighthouse
```
Tryb testowy, maksymalnie 10 domen, bez Lighthouse.

### Tylko wpprobe (bez Lighthouse)
```bash
./wpprobe_audit_script.sh --skip-lighthouse 'limit=20'
```
Pomija Lighthouse, pobiera maksymalnie 20 domen.

## Konfiguracja

### Endpoint API
Bazowy endpoint API jest skonfigurowany w zmiennej:
```bash
API_BASE_ENDPOINT="http://**************:3000/api/domains/to-audit"
```

### Webhook URLs
- Produkcyjny: `WEBHOOK_URL`
- Testowy: `TEST_WEEBHOOK_URL`

## Logi

Skrypt tworzy plik logów z nazwą zawierającą timestamp:
```
wpprobe_scan_YYYYMMDD_HHMMSS.log
```

W logach znajdziesz informacje o:
- Używanych parametrach API
- Końcowym URL endpoint
- Trybie działania (testowy/produkcyjny)
- Postępie skanowania każdej domeny

## Struktura katalogów

Skrypt tworzy strukturę katalogów:
```
audits/
├── {domain_id}/
│   ├── {domain_id}.csv (Lighthouse)
│   ├── {domain_id}.html (Lighthouse)
│   └── {domain_id}_wpprove.csv (WPProbe)
```

## Wymagania

Skrypt wymaga następujących narzędzi:
- `curl` - do komunikacji z API
- `jq` - do przetwarzania JSON
- `wpprobe` - do skanowania WordPress
- `lighthouse` - do audytu wydajności (opcjonalne z --skip-lighthouse)

## Rozwiązywanie problemów

### Błąd "Wymagane narzędzie nie jest zainstalowane"
Zainstaluj brakujące narzędzie lub użyj odpowiedniej flagi (np. --skip-lighthouse).

### Błąd połączenia z API
Sprawdź:
- Dostępność serwera API
- Poprawność parametrów
- Połączenie sieciowe

### Problemy z Lighthouse
Użyj flagi `--skip-lighthouse` aby pominąć skanowanie Lighthouse.
