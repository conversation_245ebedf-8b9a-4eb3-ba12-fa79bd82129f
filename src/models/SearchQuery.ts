import mongoose, { Document, Schema } from 'mongoose';

export interface ISearchQuery extends Document {
  query: string;
  category: string;
  location?: string;
  page: number;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

const SearchQuerySchema: Schema = new Schema({
  query: {
    type: String,
    required: [true, 'Query jest wymagane'],
    trim: true,
    maxlength: [500, 'Query nie może być dłuższe niż 500 znaków']
  },
  category: {
    type: String,
    required: [true, 'Kategoria jest wymagana'],
    trim: true,
    maxlength: [100, 'Kategoria nie może być dłuższa niż 100 znaków']
  },
  location: {
    type: String,
    required: false,
    trim: true,
    maxlength: [200, 'Lokalizacja nie może być dłuższa niż 200 znaków']
  },
  page: {
    type: Number,
    required: true,
    min: [1, 'Numer strony musi być wię<PERSON><PERSON> niż 0'],
    default: 1
  },
  userId: {
    type: String,
    required: [true, 'ID użytkownika jest wymagane']
  }
}, {
  timestamps: true
});

// Indeks złożony dla szybszego wyszukiwania zapytań użytkownika
SearchQuerySchema.index({ userId: 1, query: 1, category: 1, location: 1 });

// Indeks dla wyszukiwania po query i kategorii
SearchQuerySchema.index({ query: 1, category: 1 });

// Indeks dla wyszukiwania po lokalizacji
SearchQuerySchema.index({ location: 1 });

export default mongoose.models.SearchQuery || mongoose.model<ISearchQuery>('SearchQuery', SearchQuerySchema);
