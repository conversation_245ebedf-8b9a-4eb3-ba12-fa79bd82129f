import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';

// POST endpoint to improve audit content using AI
export async function POST(request: NextRequest) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const requestBody = await request.json();
    const { prompt, currentContent } = requestBody;

    // Validate input
    if (!prompt || !prompt.trim()) {
      return NextResponse.json(
        {
          success: false,
          error: 'Prompt is required'
        },
        { status: 400 }
      );
    }

    if (!currentContent || !currentContent.trim()) {
      return NextResponse.json(
        {
          success: false,
          error: 'Current content is required'
        },
        { status: 400 }
      );
    }

    // Prepare the AI prompt
    const systemPrompt = `Je<PERSON>ś ekspertem od audytów bezpieczeństwa stron internetowych. Twoim zadaniem jest poprawienie i ulepszenie treści audytu zgodnie z instrukcjami użytkownika.

Zasady:
1. Zachowaj format HTML jeśli jest obecny
2. Popraw błędy językowe i stylistyczne
3. Dodaj szczegóły techniczne jeśli są potrzebne
4. Zachowaj profesjonalny ton
5. Struktura powinna być logiczna i czytelna
6. Używaj polskiego języka
7. Zwróć tylko poprawioną treść bez dodatkowych komentarzy

Obecna treść audytu:
${currentContent}

Instrukcje użytkownika:
${prompt}

Poprawiona treść audytu:`;

    // Call OpenRouter API
    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'X-Title': 'AI Studio - Audit Improvement',
      },
      body: JSON.stringify({
        model: 'anthropic/claude-3.5-sonnet',
        messages: [
          {
            role: 'user',
            content: systemPrompt
          }
        ],
        max_tokens: 4000,
        temperature: 0.7,
      }),
    });

    if (!openRouterResponse.ok) {
      const errorData = await openRouterResponse.text();
      console.error('OpenRouter API error:', errorData);
      throw new Error('Failed to communicate with AI service');
    }

    const aiResponse = await openRouterResponse.json();

    if (!aiResponse.choices || !aiResponse.choices[0] || !aiResponse.choices[0].message) {
      throw new Error('Invalid response from AI service');
    }

    const improvedContent = aiResponse.choices[0].message.content.trim();

    return NextResponse.json({
      success: true,
      improvedContent,
      originalLength: currentContent.length,
      improvedLength: improvedContent.length,
    });

  } catch (error) {
    console.error('Error improving audit content:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error occurred while improving content'
      },
      { status: 500 }
    );
  }
}
