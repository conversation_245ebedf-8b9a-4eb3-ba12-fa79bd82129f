import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next'; // Changed import path
import { Session } from 'next-auth'; // Added Session import
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Settings from '@/models/Settings';
import { v4 as uuidv4 } from 'uuid';

interface MetadataButton {
  id: string;
  label: string;
  key: string;
  value: string | number | boolean; // Can be string, number, boolean, etc.
  icon: string;
  description?: string;
  order?: number;
}

// GET - Pobierz przyciski metadata użytkownika
export async function GET() {
  try {
    const session = (await getServerSession(authOptions)) as Session | null; // Explicitly cast session

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    let settings = await Settings.findOne({ userId: session.user.id });

    if (!settings) {
      // Utwórz domyślne ustawienia jeśli nie istnieją
      settings = new Settings({
        userId: session.user.id,
        metadataButtons: []
      });
      await settings.save();
    }

    return NextResponse.json({
      success: true,
      metadataButtons: settings.metadataButtons.sort((a: MetadataButton, b: MetadataButton) => (a.order || 0) - (b.order || 0)) // Added type and nullish coalescing
    });

  } catch (error) {
    console.error('Błąd pobierania przycisków metadata:', error);
    return NextResponse.json(
      { success: false, error: 'Wystąpił błąd podczas pobierania przycisków metadata' },
      { status: 500 }
    );
  }
}

// POST - Dodaj nowy przycisk metadata
export async function POST(request: NextRequest) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null; // Explicitly cast session

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { label, key, value, icon, description } = body;

    // Walidacja
    if (!label || !label.trim()) {
      return NextResponse.json(
        { success: false, error: 'Etykieta przycisku jest wymagana' },
        { status: 400 }
      );
    }

    if (!key || !key.trim()) {
      return NextResponse.json(
        { success: false, error: 'Klucz metadata jest wymagany' },
        { status: 400 }
      );
    }

    if (value === undefined || value === null) {
      return NextResponse.json(
        { success: false, error: 'Wartość jest wymagana' },
        { status: 400 }
      );
    }

    if (!icon || !icon.trim()) {
      return NextResponse.json(
        { success: false, error: 'Ikona jest wymagana' },
        { status: 400 }
      );
    }

    await connectDB();

    let settings = await Settings.findOne({ userId: session.user.id });

    if (!settings) {
      settings = new Settings({
        userId: session.user.id,
        metadataButtons: []
      });
    }

    // Sprawdź czy klucz już istnieje
    const existingButton = settings.metadataButtons.find((btn: MetadataButton) => btn.key === key.trim());
    if (existingButton) {
      return NextResponse.json(
        { success: false, error: 'Przycisk z tym kluczem już istnieje' },
        { status: 400 }
      );
    }

    // Dodaj nowy przycisk
    const newButton = {
      id: uuidv4(),
      label: label.trim(),
      key: key.trim(),
      value,
      icon: icon.trim(),
      description: description?.trim() || '',
      order: settings.metadataButtons.length
    };

    settings.metadataButtons.push(newButton);
    await settings.save();

    return NextResponse.json({
      success: true,
      message: 'Przycisk metadata został dodany',
      button: newButton
    });

  } catch (error) {
    console.error('Błąd dodawania przycisku metadata:', error);
    return NextResponse.json(
      { success: false, error: 'Wystąpił błąd podczas dodawania przycisku metadata' },
      { status: 500 }
    );
  }
}
