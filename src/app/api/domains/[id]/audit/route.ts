import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';

// POST endpoint do zapisywania treści audytu domeny (publiczny dostęp)
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const { id } = await params;
    const requestBody = await request.json();
    const { content } = requestBody;

    // Walidacja - sprawdź czy content jest niepuste
    if (!content || typeof content !== 'string' || content.trim().length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Content field is required and cannot be empty'
        },
        { status: 400 }
      );
    }

    // Sprawdź czy domena istnieje (bez sprawdzania userId - publiczny dostęp)
    const domain = await Domain.findById(id);

    if (!domain) {
      return NextResponse.json(
        {
          success: false,
          error: 'Domain not found'
        },
        { status: 404 }
      );
    }

    // Przygotuj dane do aktualizacji
    const currentDate = new Date();
    const updateObject = {
      auditContent: content.trim(),
      metadata: {
        ...domain.metadata,
        hasAudit: true,
        // Zachowaj oryginalną datę audytu jeśli już istnieje (z execute-audit)
        dateAudit: domain.metadata?.dateAudit || currentDate.toISOString()
      },
      updatedAt: currentDate
    };

    // Aktualizuj domenę
    const updatedDomain = await Domain.findByIdAndUpdate(
      id,
      updateObject,
      { new: true }
    );

    if (!updatedDomain) {
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to update domain'
        },
        { status: 500 }
      );
    }

    // Zwróć odpowiedź zgodnie ze specyfikacją
    return NextResponse.json({
      success: true,
      message: 'Audit content saved successfully',
      domain: {
        id: updatedDomain._id,
        auditContent: updatedDomain.auditContent,
        metadata: {
          hasAudit: updatedDomain.metadata?.hasAudit,
          dateAudit: updatedDomain.metadata?.dateAudit
        }
      }
    });

  } catch (error) {
    console.error('Błąd zapisywania treści audytu:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error occurred while saving audit content'
      },
      { status: 500 }
    );
  }
}
