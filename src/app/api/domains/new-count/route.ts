import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next'; // Changed import path
import { Session } from 'next-auth'; // Added Session import
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';

export async function GET() { // Renamed request to _request
  try {
    const session = (await getServerSession(authOptions)) as Session | null; // Explicitly cast session

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    // Policz domeny użytkownika ze statusem 'new'
    const count = await Domain.countDocuments({
      userId: session.user.id,
      status: 'new'
    });

    return NextResponse.json({
      success: true,
      count
    });

  } catch (error) {
    console.error('Błąd liczenia nowych domen:', error);
    return NextResponse.json(
      { error: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> błąd podczas liczenia nowych domen' },
      { status: 500 }
    );
  }
}
