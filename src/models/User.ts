import mongoose, { Document, Schema } from 'mongoose';

export interface IUser extends Document {
  email: string;
  password: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
}

const UserSchema: Schema = new Schema({
  email: {
    type: String,
    required: [true, 'Email jest wymagany'],
    lowercase: true,
    trim: true,
    match: [
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
      'Podaj prawidłowy adres email'
    ]
  },
  password: {
    type: String,
    required: [true, 'Hasło jest wymagane'],
    minlength: [6, '<PERSON>ło musi mieć co najmniej 6 znaków']
  },
  name: {
    type: String,
    required: [true, 'Imię jest wymagane'],
    trim: true,
    maxlength: [50, 'Imię nie może być dłuższe niż 50 znaków']
  }
}, {
  timestamps: true
});

// Indeks dla szybszego wyszukiwania po email (unique)
UserSchema.index({ email: 1 }, { unique: true });

export default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);
