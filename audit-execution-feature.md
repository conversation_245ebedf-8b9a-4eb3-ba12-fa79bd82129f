# Funkcjonalność Wykonywania Audytu

## Opis
Dodano funkcjonalność wykonywania audytu dla domen, kt<PERSON>re jeszcze nie mają przeprowadzonego audytu. Gdy domena nie ma audytu, wy<PERSON><PERSON><PERSON><PERSON>y jest przycisk "Wykonaj audyt", który wysyła dane domeny do zewnętrznego webhook.

## Zmiany w kodzie

### 1. Zmienna środowiskowa (.env)
```
# Webhook URL for audit execution
AUDIT_WEBHOOK_URL=http://0.0.0.0:5678/webhook-test/5830d0ec-c423-4d07-b149-5c866781a307
```

### 2. Nowy API endpoint
**Plik:** `src/app/api/domains/[id]/execute-audit/route.ts`

Endpoint POST do wykonywania audytu domeny:
- Sprawdza autoryzację użytkownika
- Weryfikuje czy domena należy do użytkownika
- Sprawdza czy domena nie ma już audytu
- Wysyła dane domeny na webhook
- Aktualizuje status domeny na "in_audit"

**<PERSON> wysyłane na webhook:**
```json
{
  "domainId": "string",
  "domain": "string",
  "fullDomain": "string",
  "protocol": "string",
  "category": "string",
  "userId": "string",
  "requestedAt": "ISO date string"
}
```

### 3. Poprawka API pobierania domeny
**Plik:** `src/app/api/domains/[id]/route.ts`

Poprawiono logikę sprawdzania czy domena ma audyt:
```typescript
secureAudit: !!(domain.auditContent && domain.auditContent.trim().length > 0)
```

### 4. Zmiany w interfejsie użytkownika
**Plik:** `src/app/dashboard/domains/[id]/page.tsx`

#### Dodano:
- Stan `isExecutingAudit` do śledzenia procesu wykonywania audytu
- Funkcję `executeAudit()` do obsługi żądania audytu
- Pole `status` do interfejsu `Domain`
- Poprawiono funkcję `formatMetadataValue()` do obsługi wszystkich typów

#### Zmodyfikowano sekcję audytu:
- Gdy domena ma audyt: wyświetla "Audyt OK" z zieloną ikoną
- Gdy domena nie ma audytu: wyświetla "Nie przeprowadzono" + przycisk "Wykonaj audyt"
- Przycisk jest wyłączony gdy:
  - Trwa wykonywanie audytu (pokazuje "Wykonywanie...")
  - Domena ma status "in_audit" (pokazuje "W trakcie audytu")

## Logika działania

1. **Sprawdzenie audytu:** System sprawdza czy domena ma pole `auditContent` z treścią
2. **Wyświetlenie przycisku:** Jeśli nie ma audytu, pokazuje przycisk "Wykonaj audyt"
3. **Kliknięcie przycisku:** Wysyła POST na `/api/domains/[id]/execute-audit`
4. **Walidacja:** API sprawdza autoryzację i czy domena nie ma już audytu
5. **Webhook:** Wysyła dane domeny na skonfigurowany URL webhook
6. **Aktualizacja:** Zmienia status domeny na "in_audit"
7. **Feedback:** Pokazuje komunikat sukcesu i odświeża dane domeny

## Bezpieczeństwo
- Endpoint wymaga autoryzacji użytkownika
- Sprawdza własność domeny przed wykonaniem akcji
- Waliduje czy domena nie ma już audytu
- Obsługuje błędy webhook i pokazuje odpowiednie komunikaty

## Testowanie
1. Zaloguj się do aplikacji
2. Przejdź do szczegółów domeny bez audytu
3. Sprawdź czy wyświetla się przycisk "Wykonaj audyt"
4. Kliknij przycisk i sprawdź czy:
   - Pokazuje się komunikat sukcesu
   - Status domeny zmienia się na "in_audit"
   - Przycisk zmienia się na "W trakcie audytu"

## Przeniesienie funkcjonalności

**UWAGA:** Funkcjonalność została przeniesiona z `/dashboard/domeny/[id]` do `/dashboard/domains/[id]` zgodnie z żądaniem użytkownika.

### Zmiany:
- Funkcjonalność wykonywania audytu jest teraz dostępna w `/dashboard/domains/[id]/page.tsx`
- Wszystkie funkcje i stany zostały skopiowane z oryginalnej lokalizacji
- API endpoint pozostaje bez zmian: `/api/domains/[id]/execute-audit`

Funkcjonalność jest gotowa do użycia! 🎉
