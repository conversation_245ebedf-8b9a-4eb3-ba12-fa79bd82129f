import mongoose, { Document, Schema } from 'mongoose';

export interface ILog extends Document {
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  source?: string;
  userId?: string;
  metadata?: Record<string, string | number | boolean | Date | null>;
  createdAt: Date;
  updatedAt: Date;
}

const LogSchema: Schema = new Schema({
  level: {
    type: String,
    required: [true, 'Poziom loga jest wymagany'],
    enum: {
      values: ['info', 'warn', 'error', 'debug'],
      message: 'Poziom loga musi być jednym z: info, warn, error, debug'
    }
  },
  message: {
    type: String,
    required: [true, 'Wiadomość loga jest wymagana'],
    trim: true,
    maxlength: [1000, 'Wiadomość loga nie może być dłuższa niż 1000 znaków']
  },
  source: {
    type: String,
    trim: true,
    maxlength: [100, 'Źródło loga nie może być dłuższe niż 100 znaków']
  },
  userId: {
    type: String,
    required: false
  },
  metadata: {
    type: Schema.Types.Mixed,
    required: false
  }
}, {
  timestamps: true
});

// Indeks dla szybszego wyszukiwania logów po poziomie i dacie
LogSchema.index({ level: 1, createdAt: -1 });

// Indeks dla wyszukiwania logów użytkownika
LogSchema.index({ userId: 1, createdAt: -1 });

// Indeks dla wyszukiwania po źródle
LogSchema.index({ source: 1, createdAt: -1 });

export default mongoose.models.Log || mongoose.model<ILog>('Log', LogSchema);
