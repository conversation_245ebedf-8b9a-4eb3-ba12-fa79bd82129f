import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Contact from '@/models/Contact';

// DELETE endpoint do usuwania kontaktu
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { id } = await params;

    // Sprawdź czy kontakt istnieje i należy do użytkownika lub jest publiczny
    const contact = await Contact.findOne({
      _id: id,
      $or: [
        { userId: "0" }, // Kontakty publiczne
        { userId: session.user.id } // Kontakty użytkownika
      ]
    });

    if (!contact) {
      return NextResponse.json(
        { error: 'Kontakt nie został znaleziony' },
        { status: 404 }
      );
    }

    // Sprawdź czy użytkownik może usunąć ten kontakt
    // Tylko kontakty publiczne (userId = "0") lub własne kontakty użytkownika
    if (contact.userId !== "0" && contact.userId !== session.user.id) {
      return NextResponse.json(
        { error: 'Brak uprawnień do usunięcia tego kontaktu' },
        { status: 403 }
      );
    }

    // Usuń kontakt
    await Contact.findByIdAndDelete(id);

    return NextResponse.json({
      success: true,
      message: 'Kontakt został usunięty'
    });

  } catch (error) {
    console.error('Błąd usuwania kontaktu:', error);
    return NextResponse.json(
      { error: 'Błąd serwera' },
      { status: 500 }
    );
  }
}
