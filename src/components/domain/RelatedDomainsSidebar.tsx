"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Globe, ChevronRight } from "lucide-react";
import Link from "next/link";
import { cn } from "@/lib/utils";

interface Domain {
  id: string;
  domain: string;
  protocol: string;
  fullDomain: string;
  category: string;
  linkCount: number;
  status: "new" | "accepted" | "rejected" | "closed" | "in_progress" | "in_audit" | "production" | "hold";
  cms?: string;
  ocena?: "wysoka" | "niska" | "brak";
  createdAt: string;
  updatedAt: string;
}

interface RelatedDomainsResponse {
  success: boolean;
  domains: Domain[];
  totalCount: number;
}

interface RelatedDomainsSidebarProps {
  currentDomain: Domain;
  className?: string;
}



export function RelatedDomainsSidebar({ currentDomain, className }: RelatedDomainsSidebarProps) {
  const [relatedDomains, setRelatedDomains] = useState<Domain[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);

  useEffect(() => {
    const fetchRelatedDomains = async () => {
      setIsLoading(true);
      try {
        const params = new URLSearchParams({
          status: "new", // Zawsze pokazujemy domeny ze statusem "new"
          cms: "wordpress", // Tylko domeny z WordPress CMS
          limit: "20", // Zwiększamy limit do 20 domen
        });

        const response = await fetch(`/api/domains?${params}`);

        if (!response.ok) {
          throw new Error("Błąd pobierania domen");
        }

        const data: RelatedDomainsResponse = await response.json();

        // Filtrujemy aktualną domenę z wyników
        const filteredDomains = data.domains.filter(domain => domain.id !== currentDomain.id);
        setRelatedDomains(filteredDomains);
        setTotalCount(data.totalCount - 1); // Odejmujemy aktualną domenę
      } catch (error) {
        console.error("Błąd pobierania domen:", error);
        setRelatedDomains([]);
        setTotalCount(0);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRelatedDomains();
  }, [currentDomain.id]);

  if (isLoading) {
    return (
      <Card className={cn("sidebar-height sticky top-20  overflow-y-auto gap-0", className)}>
        <CardHeader className="pb-0">
          <CardTitle className="text-lg flex items-center gap-y-2">
            <Globe className="h-5 w-5" />
            Nowe domeny WordPress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-sm text-muted-foreground">Ładowanie...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (relatedDomains.length === 0) {
    return (
      <Card className={cn(" sticky top-20 sidebar-height overflow-y-auto gap-0" , className)}>
        <CardHeader className="pb-0">
          <CardTitle className="text-lg flex items-center gap-y-2">
            <Globe className="h-5 w-5" />
            Nowe domeny WordPress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-sm text-muted-foreground">
              Brak nowych domen WordPress do wyświetlenia
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("sidebar-height sticky top-20 flex flex-col  overflow-y-auto gap-0", className)}>
      <CardHeader className="pb-0 flex-shrink-0">
        <CardTitle className="text-lg flex items-center gap-y-2">
          <Globe className="h-5 w-5" />
          Nowe domeny WordPress
        </CardTitle>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
            Nowe
          </Badge>
          <Badge variant="outline" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
            WordPress
          </Badge>
          <span className="text-sm text-muted-foreground">
            {relatedDomains.length} {relatedDomains.length === 1 ? 'domena' : 'domen'}
          </span>
        </div>
      </CardHeader>
      <CardContent className="p-0 flex-1 flex flex-col min-h-0">
        <ScrollArea className="flex-1">
          <div className="space-y-3 p-4">
            {relatedDomains.map((domain) => (
              <div
                key={domain.id}
                className="group border rounded-lg p-4 hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-start justify-between gap-3">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 pt-1">
                      <Globe className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                      <Link
                        href={`/dashboard/domains/${domain.id}`}
                        className="text-sm font-medium hover:text-primary transition-colors truncate"
                        title={domain.domain}
                      >
                        {domain.domain}
                      </Link>
                    </div>

                  </div>
                  <div className="flex flex-col gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      asChild
                      className="h-7 w-7 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      asChild
                      className="h-7 w-7 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <Link
                        href={`/dashboard/domains/${domain.id}`}
                        title="Zobacz szczegóły"
                      >
                        <ChevronRight className="h-3.5 w-3.5" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
        {totalCount > relatedDomains.length && (
          <div className="p-4 border-t flex-shrink-0">
            <Button
              variant="outline"
              size="sm"
              asChild
              className="w-full"
            >
              <Link
                href={`/dashboard/domains?status=new&cms=wordpress`}
              >
                Zobacz wszystkie nowe WordPress ({totalCount})
              </Link>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
