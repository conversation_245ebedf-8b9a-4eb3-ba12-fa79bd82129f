import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next'; // Changed import path
import { Session } from 'next-auth'; // Added Session import
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';
import EmailHistory from '@/models/EmailHistory';

interface EmailHistoryFilter {
  domainId: string;
  userId: string;
  status?: 'sent' | 'failed' | 'pending';
  emailType?: 'audit' | 'notification' | 'other';
}

// GET endpoint to fetch email history for a domain
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null; // Explicitly cast session

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const emailType = searchParams.get('emailType');

    // Verify domain ownership
    const domain = await Domain.findOne({
      _id: id,
      userId: session.user.id
    });

    if (!domain) {
      return NextResponse.json(
        {
          success: false,
          error: 'Domain not found or access denied'
        },
        { status: 404 }
      );
    }

    // Build filter for email history
    const filter: EmailHistoryFilter = {
      domainId: id,
      userId: session.user.id
    };

    if (status && ['sent', 'failed', 'pending'].includes(status)) {
      filter.status = status as 'sent' | 'failed' | 'pending';
    }

    if (emailType && ['audit', 'notification', 'other'].includes(emailType)) {
      filter.emailType = emailType as 'audit' | 'notification' | 'other';
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Fetch email history with pagination
    const [emailHistory, totalCount] = await Promise.all([
      EmailHistory.find(filter)
        .sort({ sentAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      EmailHistory.countDocuments(filter)
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    // Format response (exclude emailContent for list view for performance)
    const formattedHistory = emailHistory.map(entry => ({
      id: entry._id,
      recipientEmail: entry.recipientEmail,
      subject: entry.subject,
      emailType: entry.emailType,
      status: entry.status,
      sentAt: entry.sentAt,
      metadata: entry.metadata,
      hasContent: !!entry.emailContent,
      createdAt: entry.createdAt,
      updatedAt: entry.updatedAt
    }));

    return NextResponse.json({
      success: true,
      data: {
        emailHistory: formattedHistory,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          limit,
          hasNextPage,
          hasPrevPage
        },
        domain: {
          id: domain._id,
          domain: domain.domain,
          fullDomain: domain.fullDomain
        }
      }
    });

  } catch (error) {
    console.error('Error fetching email history:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error occurred while fetching email history'
      },
      { status: 500 }
    );
  }
}

// DELETE endpoint to remove email history entry
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null; // Explicitly cast session

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const historyId = searchParams.get('historyId');

    if (!historyId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Email history ID is required'
        },
        { status: 400 }
      );
    }

    // Verify domain ownership
    const domain = await Domain.findOne({
      _id: id,
      userId: session.user.id
    });

    if (!domain) {
      return NextResponse.json(
        {
          success: false,
          error: 'Domain not found or access denied'
        },
        { status: 404 }
      );
    }

    // Find and delete email history entry
    const emailHistory = await EmailHistory.findOneAndDelete({
      _id: historyId,
      domainId: id,
      userId: session.user.id
    });

    if (!emailHistory) {
      return NextResponse.json(
        {
          success: false,
          error: 'Email history entry not found'
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Email history entry deleted successfully',
      deletedEntry: {
        id: emailHistory._id,
        recipientEmail: emailHistory.recipientEmail,
        subject: emailHistory.subject,
        sentAt: emailHistory.sentAt
      }
    });

  } catch (error) {
    console.error('Error deleting email history:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error occurred while deleting email history'
      },
      { status: 500 }
    );
  }
}
