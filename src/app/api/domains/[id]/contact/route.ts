import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth'; // Import Session type
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';

interface ContactData {
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  notes: string;
}

// GET endpoint do pobierania danych kontaktowych domeny (publiczny dostęp)
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const { id } = await params;

    // Sprawdź czy domena istnieje (bez sprawdzania userId - publiczny dostęp)
    const domain = await Domain.findById(id);

    if (!domain) {
      return NextResponse.json(
        { error: 'Domena nie została znaleziona' },
        { status: 404 }
      );
    }

    // Pobierz dane kontaktowe z metadanych
    const contactData: ContactData = {
      companyName: domain.contact_data?.contact_companyName || '',
      contactPerson: domain.contact_data?.contact_contactPerson || '',
      email: domain.contact_data?.contact_email || '',
      phone: domain.contact_data?.contact_phone || '',
      address: domain.contact_data?.contact_address || '',
      notes: domain.contact_data?.contact_notes || '',
    };

    return NextResponse.json({
      success: true,
      contact: contactData
    });

  } catch (error) {
    console.error('Błąd pobierania danych kontaktowych:', error);
    return NextResponse.json(
      { error: 'Błąd serwera' },
      { status: 500 }
    );
  }
}

// PATCH endpoint do aktualizacji danych kontaktowych domeny
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null; // Explicitly cast session

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const contactData: ContactData = await request.json();

    // Walidacja danych
    if (typeof contactData !== 'object') {
      return NextResponse.json(
        { error: 'Nieprawidłowe dane kontaktowe' },
        { status: 400 }
      );
    }

    await connectDB();

    const { id } = await params;

    // Sprawdź czy domena istnieje i należy do użytkownika
    const domain = await Domain.findOne({
      _id: id,
      userId: session.user.id
    });

    if (!domain) {
      return NextResponse.json(
        { error: 'Domena nie została znaleziona' },
        { status: 404 }
      );
    }

    // Przygotuj aktualizację metadanych
    const updateObject: Record<string, string | Date> = {
      'contact_data.contact_companyName': contactData.companyName || '',
      'contact_data.contact_contactPerson': contactData.contactPerson || '',
      'contact_data.contact_email': contactData.email || '',
      'contact_data.contact_phone': contactData.phone || '',
      'contact_data.contact_address': contactData.address || '',
      'contact_data.contact_notes': contactData.notes || '',
      'contact_data.contact_lastUpdated': new Date(),
    };

    // Aktualizuj domenę
    const updatedDomain = await Domain.findByIdAndUpdate(
      id,
      { $set: updateObject },
      { new: true }
    );

    return NextResponse.json({
      success: true,
      message: 'Dane kontaktowe zostały zaktualizowane',
      contact: {
        companyName: updatedDomain.contact_data?.contact_companyName || '',
        contactPerson: updatedDomain.contact_data?.contact_contactPerson || '',
        email: updatedDomain.contact_data?.contact_email || '',
        phone: updatedDomain.contact_data?.contact_phone || '',
        address: updatedDomain.contact_data?.contact_address || '',
        notes: updatedDomain.contact_data?.contact_notes || '',
      }
    });

  } catch (error) {
    console.error('Błąd aktualizacji danych kontaktowych:', error);
    return NextResponse.json(
      { error: 'Błąd serwera' },
      { status: 500 }
    );
  }
}
