@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.5rem; /* Zmniejszony dla ostrzejszego wyglądu */
  --background: oklch(0.985 0.005 240); /* Bardzo jasny, lekko chłodny szary */
  --foreground: oklch(0.12 0.01 240); /* Ciemny, lekko chłodny szary */
  --card: oklch(1 0 0); /* Biały dla kart, dla kontrastu */
  --card-foreground: oklch(0.12 0.01 240);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.12 0.01 240);
  --primary: oklch(0.52 0.16 265); /* Nowoczesny, żywy fioletowo-niebieski */
  --primary-foreground: oklch(0.99 0.005 265); /* Bardzo jasny dla kontrastu z primary */
  --secondary: oklch(0.95 0.01 240); /* Jasny, chłodny szary */
  --secondary-foreground: oklch(0.35 0.01 240); /* Średnio-ciemny, chłodny szary */
  --muted: oklch(0.95 0.01 240);
  --muted-foreground: oklch(0.5 0.01 240); /* Stonowany tekst */
  --accent: oklch(0.95 0.01 240);
  --accent-foreground: oklch(0.35 0.01 240);
  --destructive: oklch(0.58 0.25 25); /* Czerwień dla błędów */
  --border: oklch(0.90 0.008 240); /* Subtelna, chłodna szara ramka */
  --input: oklch(0.90 0.008 240);
  --ring: oklch(0.6 0.16 265 / 0.5); /* Ring w kolorze primary, półprzezroczysty */
  --chart-1: oklch(0.65 0.22 40);
  --chart-2: oklch(0.6 0.12 185);
  --chart-3: oklch(0.4 0.07 225);
  --chart-4: oklch(0.83 0.19 85);
  --chart-5: oklch(0.77 0.19 70);
  --sidebar: oklch(0.97 0.005 240); /* Lekko jaśniejszy sidebar */
  --sidebar-foreground: oklch(0.12 0.01 240);
  --sidebar-primary: oklch(0.52 0.16 265);
  --sidebar-primary-foreground: oklch(0.99 0.005 265);
  --sidebar-accent: oklch(0.95 0.01 240);
  --sidebar-accent-foreground: oklch(0.35 0.01 240);
  --sidebar-border: oklch(0.90 0.008 240);
  --sidebar-ring: oklch(0.6 0.16 265 / 0.5);
}

.dark {
  --background: oklch(0.12 0.015 240); /* Głęboki, chłodny granat/szary */
  --foreground: oklch(0.96 0.005 240); /* Bardzo jasny, lekko chłodny szary */
  --card: oklch(0.17 0.015 240); /* Nieco jaśniejszy od tła dla kart */
  --card-foreground: oklch(0.96 0.005 240);
  --popover: oklch(0.17 0.015 240);
  --popover-foreground: oklch(0.96 0.005 240);
  --primary: oklch(0.6 0.17 265); /* Jaśniejsza wersja primary z motywu jasnego */
  --primary-foreground: oklch(0.1 0.01 265); /* Ciemny dla kontrastu z primary */
  --secondary: oklch(0.22 0.015 240); /* Ciemniejszy chłodny szary */
  --secondary-foreground: oklch(0.88 0.008 240); /* Jaśniejszy chłodny szary dla tekstu */
  --muted: oklch(0.22 0.015 240);
  --muted-foreground: oklch(0.65 0.01 240); /* Stonowany tekst */
  --accent: oklch(0.22 0.015 240);
  --accent-foreground: oklch(0.88 0.008 240);
  --destructive: oklch(0.65 0.22 25); /* Czerwień dla błędów w trybie ciemnym */
  --border: oklch(0.25 0.015 240 / 0.8); /* Subtelna ramka, lekko widoczna */
  --input: oklch(0.25 0.015 240 / 0.7); /* Input nieco jaśniejszy od tła */
  --ring: oklch(0.65 0.17 265 / 0.6); /* Ring w kolorze primary, półprzezroczysty */
  --chart-1: oklch(0.5 0.24 265);
  --chart-2: oklch(0.7 0.17 160);
  --chart-3: oklch(0.77 0.19 70);
  --chart-4: oklch(0.63 0.27 300);
  --chart-5: oklch(0.65 0.25 15);
  --sidebar: oklch(0.15 0.015 240); /* Sidebar nieco ciemniejszy od głównego tła */
  --sidebar-foreground: oklch(0.96 0.005 240);
  --sidebar-primary: oklch(0.6 0.17 265);
  --sidebar-primary-foreground: oklch(0.1 0.01 265);
  --sidebar-accent: oklch(0.22 0.015 240);
  --sidebar-accent-foreground: oklch(0.88 0.008 240);
  --sidebar-border: oklch(0.25 0.015 240 / 0.8);
  --sidebar-ring: oklch(0.65 0.17 265 / 0.6);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

.sidebar-height {
  height: calc(100vh - 6rem);
}