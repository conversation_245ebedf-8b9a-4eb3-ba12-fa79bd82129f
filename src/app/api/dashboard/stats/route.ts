import { NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';
import { getServerSession } from "next-auth/next";
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET() {
  const session = (await getServerSession(authOptions)) as Session | null;

  if (!session || !session.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    await connectDB();

    const totalDomains = await Domain.countDocuments({ userId: session.user.id });
    const newDomains = await Domain.countDocuments({ userId: session.user.id, status: 'new' });
    const acceptedDomains = await Domain.countDocuments({ userId: session.user.id, status: 'accepted' });
    const inProgressDomains = await Domain.countDocuments({ userId: session.user.id, status: 'in_progress' });
    // Add other counts as needed, e.g., for 'production' status
    // const productionDomains = await Domain.countDocuments({ userId: session.user.id, status: 'production' });

    // Pobierz 5 domen z najnowszą datą aktualizacji
    const recentDomains = await Domain.find({ userId: session.user.id })
      .sort({ updatedAt: -1 })
      .limit(5)
      .select('_id domain fullDomain category status cms updatedAt')
      .lean();

    const stats = {
      totalDomains,
      newDomains,
      acceptedDomains,
      inProgressDomains,
      recentDomains: recentDomains.map(domain => ({
        id: domain._id,
        domain: domain.domain,
        fullDomain: domain.fullDomain,
        category: domain.category,
        status: domain.status,
        cms: domain.cms,
        updatedAt: domain.updatedAt
      })),
      // productionDomains,
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
