import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Link from '@/models/Link';

export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { id } = await params;

    // Sprawdź czy link istnieje i należy do użytkownika
    const link = await Link.findOne({
      _id: id,
      userId: session.user.id
    });

    if (!link) {
      return NextResponse.json(
        { error: 'Link nie został znaleziony' },
        { status: 404 }
      );
    }

    // Usuń link
    await Link.findByIdAndDelete(id);

    return NextResponse.json({
      success: true,
      message: 'Link został usunięty'
    });

  } catch (error) {
    console.error('Błąd usuwania linku:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas usuwania linku' },
      { status: 500 }
    );
  }
}
