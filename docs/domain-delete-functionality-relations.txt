FUNKCJONALNOŚĆ USUWANIA DOMENY - RELACJE MIĘDZY KOMPONENTAMI
==============================================================

OPIS FUNKCJONALNOŚCI:
====================
Dodano możliwość usunięcia domeny z widoku jej szczegółów wraz z wszystkimi powiązanymi linkami.
Funkcjonalność wykorzystuje AlertDialog do potwierdzenia akcji usunięcia.

KOMPONENTY I PLIKI:
==================

1. API Endpoint: /api/domains/[id]/route.ts
   - Dodano metodę DELETE
   - Usuwa domenę i wszystkie powiązane linki
   - Sprawdza uprawnienia użytkownika
   - Zwraca informacje o usuniętej domenie i liczbie usuniętych linków

2. Frontend: /app/dashboard/domains/[id]/page.tsx
   - Dodano przycisk "Usuń domenę" w sekcji z przyciskami akcji
   - Wykorzystuje AlertDialog do potwierdzenia usunięcia
   - Implementuje funkcję handleDeleteDomain()
   - Po usunięciu przekierowuje do listy domen

RELACJE MIĘDZY KOMPONENTAMI:
============================

DomainDetailsPage
├── Importuje: AlertDialog, AlertDialogAction, AlertDialogCancel, etc.
├── Stan: showDeleteDialog, isDeleting
├── Funkcja: handleDeleteDomain()
├── UI: Przycisk "Usuń domenę" z ikoną Trash2
├── Dialog: AlertDialog z potwierdzeniem usunięcia
└── Nawigacja: Przekierowanie do /dashboard/domains po usunięciu

API DELETE /api/domains/[id]
├── Autoryzacja: Sprawdza session.user.id
├── Walidacja: Sprawdza czy domena należy do użytkownika
├── Operacje bazy danych:
│   ├── Usuwa powiązane linki (Link.deleteMany)
│   └── Usuwa domenę (Domain.findByIdAndDelete)
├── Odpowiedź: Informacje o usuniętej domenie i linkach
└── Obsługa błędów: Zwraca odpowiednie kody statusu

PRZEPŁYW DANYCH:
================

1. Użytkownik klika przycisk "Usuń domenę"
2. Otwiera się AlertDialog z potwierdzeniem
3. Po potwierdzeniu wywołuje się handleDeleteDomain()
4. Wysyłane jest żądanie DELETE do /api/domains/[id]
5. API usuwa domenę i powiązane linki z bazy danych
6. Zwracana jest odpowiedź z informacjami o usunięciu
7. Wyświetlany jest toast z potwierdzeniem
8. Użytkownik jest przekierowywany do listy domen

BEZPIECZEŃSTWO:
===============

- Sprawdzanie autoryzacji użytkownika
- Walidacja własności domeny
- Potwierdzenie akcji przez AlertDialog
- Nieodwracalność operacji jest jasno komunikowana

KOMPONENTY SHADCN UŻYTE:
========================

- AlertDialog - główny kontener dialogu
- AlertDialogTrigger - przycisk wywołujący dialog
- AlertDialogContent - zawartość dialogu
- AlertDialogHeader - nagłówek dialogu
- AlertDialogTitle - tytuł dialogu
- AlertDialogDescription - opis akcji
- AlertDialogFooter - stopka z przyciskami
- AlertDialogCancel - przycisk anulowania
- AlertDialogAction - przycisk potwierdzenia
- Button (variant="destructive") - przycisk usuwania
- Ikona Trash2 z lucide-react

OBSŁUGA BŁĘDÓW:
===============

- Sprawdzanie istnienia domeny
- Sprawdzanie uprawnień użytkownika
- Obsługa błędów sieciowych
- Wyświetlanie komunikatów błędów przez toast
- Zabezpieczenie przed wielokrotnym kliknięciem (isDeleting)

INTEGRACJA Z ISTNIEJĄCYM KODEM:
===============================

- Wykorzystuje istniejące modele Domain i Link
- Używa istniejącej konfiguracji bazy danych
- Integruje się z systemem autoryzacji
- Wykorzystuje istniejący system powiadomień (toast)
- Zachowuje spójność z innymi funkcjami usuwania w aplikacji
