RELACJE MIĘDZY ELEMENTAMI - OKNO LOADING MODAL W AGREGATORZE WYSZUKIWANIA

=== KOMPONENTY GŁÓWNE ===

1. AgregatorPage (src/app/dashboard/agregator/page.tsx)
   - Główny komponent strony agregatora
   - Zawiera formularz wyszukiwania i logikę obsługi
   - Zarządza stanem loading modal

2. LoadingModal (komponent wewnętrzny w AgregatorPage)
   - Komponent okna blokującego z animacją ładowania
   - Wyświetlany podczas wyszukiwania przez Serper API
   - Zawiera przycisk anulowania

=== STANY KOMPONENTU ===

1. isLoading (boolean)
   - Kontroluje stan ładowania przycisku wyszukiwania
   - Ustawiane na true podczas wyszukiwania
   - Resetowane po zakończeniu lub błędzie

2. isSearchModalOpen (boolean)
   - Kontroluje widoczność okna loading modal
   - Otwierane przy rozpoczęciu wyszukiwania
   - Zamykane po zakończeniu lub anulowaniu

3. searchAbortController (AbortController | null)
   - Kontroler do anulowania żądania HTTP
   - Tworzony przy rozpoczęciu wyszukiwania
   - Używany do przerwania fetch() w przypadku anulowania

=== PRZEPŁYW DZIAŁANIA ===

1. Użytkownik wypełnia formularz wyszukiwania
2. Kliknięcie przycisku "Wyszukaj" wywołuje onSubmit()
3. onSubmit() ustawia:
   - isLoading = true
   - isSearchModalOpen = true
   - Tworzy nowy AbortController
4. Wyświetla się LoadingModal z animacją
5. Wykonywane jest żądanie do /api/search z signal: abortController.signal
6. Po zakończeniu:
   - isSearchModalOpen = false
   - isLoading = false
   - searchAbortController = null
   - Wyświetlenie toast z rezultatem

=== OBSŁUGA ANULOWANIA ===

1. Użytkownik klika "Anuluj wyszukiwanie" w LoadingModal
2. Wywołuje się handleCancelSearch()
3. Funkcja wykonuje:
   - abortController.abort() - przerywa żądanie HTTP
   - isLoading = false
   - isSearchModalOpen = false
   - searchAbortController = null
   - Wyświetla toast informacyjny

=== KOMPONENTY UI WYKORZYSTANE ===

1. Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter
   - Z @/components/ui/dialog
   - Podstawa dla okna modal

2. DialogPortal, DialogOverlay
   - Renderowanie modal poza głównym drzewem DOM
   - Overlay z efektem blur

3. Button
   - Z @/components/ui/button
   - Przycisk anulowania w modal

4. Ikony z lucide-react:
   - Search - ikona wyszukiwania w tytule modal
   - Loader2 - ikona ładowania (nieużywana w finalnej wersji)

=== ANIMACJE CSS ===

1. Główny spinner (zewnętrzny):
   - w-20 h-20 border-4 border-primary/20 border-t-primary
   - animate-spin (domyślna animacja Tailwind)

2. Średni spinner:
   - w-16 h-16 border-4 border-transparent border-t-primary/60
   - animate-spin z animationDirection: 'reverse', animationDuration: '1.5s'

3. Wewnętrzny spinner:
   - w-12 h-12 border-2 border-transparent border-t-primary/40
   - animate-spin z animationDuration: '2s'

=== INTEGRACJA Z API ===

1. Żądanie do /api/search (POST)
   - Zawiera signal: abortController.signal
   - Umożliwia anulowanie żądania przez AbortController

2. Obsługa błędu AbortError
   - Sprawdzanie error.name === 'AbortError'
   - Brak wyświetlania błędu dla anulowanego żądania

=== STYLE I WYGLĄD ===

1. Overlay:
   - bg-black/80 backdrop-blur-sm
   - Ciemne tło z efektem rozmycia

2. Modal:
   - sm:max-w-md - maksymalna szerokość
   - [&>button]:hidden - ukrycie domyślnego przycisku zamknięcia
   - border-primary/20 - subtelna ramka

3. Animacje:
   - Wielowarstwowy spinner z różnymi prędkościami
   - Różne kierunki obrotów dla efektu wizualnego

=== KOMUNIKATY UŻYTKOWNIKA ===

1. Tytuł modal: "Wyszukiwanie w toku"
2. Opis: "Proszę czekać do końca akcji pobierania linków..."
3. Status: "Analizowanie wyników wyszukiwania..."
4. Ostrzeżenie: "Nie zamykaj tej strony podczas wyszukiwania"
5. Toast po anulowaniu: "Wyszukiwanie zostało anulowane"
6. Toast po sukcesie: Szczegółowe informacje o rezultatach

=== BEZPIECZEŃSTWO ===

1. Modal nie może być zamknięty przez ESC lub kliknięcie overlay
2. onOpenChange={() => {}} - pusta funkcja blokuje zamknięcie
3. [&>button]:hidden - ukrycie domyślnego przycisku X
4. Jedyny sposób zamknięcia to zakończenie wyszukiwania lub anulowanie

=== ZALEŻNOŚCI ===

1. React hooks: useState
2. Komponenty UI: shadcn/ui (Dialog, Button)
3. Ikony: lucide-react
4. Notyfikacje: sonner (toast)
5. Animacje: Tailwind CSS
6. HTTP: fetch API z AbortController
