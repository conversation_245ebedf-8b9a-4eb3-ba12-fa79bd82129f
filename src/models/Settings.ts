import mongoose, { Document, Schema } from 'mongoose';

export interface IMetadataButton {
  id: string;
  label: string;
  key: string;
  value: string | number | boolean;
  icon: string;
  description?: string;
  order: number;
}

export interface ISettings extends Document {
  userId: string;
  metadataButtons: IMetadataButton[];
  testEmail?: string;
  createdAt: Date;
  updatedAt: Date;
}

const MetadataButtonSchema: Schema = new Schema({
  id: {
    type: String,
    required: true
  },
  label: {
    type: String,
    required: [true, 'Etykieta przycisku jest wymagana'],
    trim: true,
    maxlength: [50, 'Etykieta nie może być dłuższa niż 50 znaków']
  },
  key: {
    type: String,
    required: [true, 'Klucz metadata jest wymagany'],
    trim: true,
    maxlength: [100, 'Klucz nie może być dłuższy niż 100 znaków']
  },
  value: {
    type: Schema.Types.Mixed,
    required: [true, 'Wartość jest wymagana']
  },
  icon: {
    type: String,
    required: [true, 'Ikona jest wymagana'],
    trim: true,
    maxlength: [50, 'Nazwa ikony nie może być dłuższa niż 50 znaków']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [200, 'Opis nie może być dłuższy niż 200 znaków']
  },
  order: {
    type: Number,
    required: true,
    default: 0
  }
}, { _id: false });

const SettingsSchema: Schema = new Schema({
  userId: {
    type: String,
    required: [true, 'ID użytkownika jest wymagane']
  },
  metadataButtons: {
    type: [MetadataButtonSchema],
    default: []
  },
  testEmail: {
    type: String,
    trim: true,
    validate: {
      validator: function(email: string) {
        if (!email) return true; // Allow empty values
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
      },
      message: 'Nieprawidłowy format adresu email'
    },
    maxlength: [100, 'Adres email nie może być dłuższy niż 100 znaków']
  }
}, {
  timestamps: true
});

// Indeks dla szybszego wyszukiwania po userId
SettingsSchema.index({ userId: 1 }, { unique: true });

export default mongoose.models.Settings || mongoose.model<ISettings>('Settings', SettingsSchema);
