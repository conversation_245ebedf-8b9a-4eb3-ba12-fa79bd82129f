import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import SearchQuery from '@/models/SearchQuery';
import Link from '@/models/Link';
import Domain from '@/models/Domain';
import { checkDomainCms } from '@/lib/cms-checker';

interface SerperResult {
  title: string;
  link: string;
  snippet?: string;
}

interface SerperResponse {
  organic: SerperResult[];
}

export async function POST(request: NextRequest) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { query, category, location, page } = await request.json();

    if (!query || !category) {
      return NextResponse.json(
        { error: 'Query i kategoria są wymagane' },
        { status: 400 }
      );
    }

    await connectDB();

    // Określ numer strony
    let pageNumber: number;

    if (page && page > 0) {
      // Użytkownik wybrał konkretną stronę
      pageNumber = page;
    } else {
      // Automatyczna paginacja - sprawdź czy istnieje już zapytanie dla tego użytkownika
      const existingQuery = await SearchQuery.findOne({
        userId: session.user.id,
        query: query.trim(),
        category: category.trim(),
        location: location?.trim() || null
      }).sort({ page: -1 });

      pageNumber = existingQuery ? existingQuery.page + 1 : 1;
    }

    // Wykonaj zapytanie do Serper API
    const serperResponse = await fetch('https://google.serper.dev/search', {
      method: 'POST',
      headers: {
        'X-API-KEY': process.env.SERPER_API_KEY!,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        q: query,
        num: 100,
        page: pageNumber,
        ...(location && location.trim() && { location: location.trim() })
      })
    });

    if (!serperResponse.ok) {
      throw new Error('Błąd API Serper');
    }

    const serperData: SerperResponse = await serperResponse.json();

    // Zapisz zapytanie wyszukiwania
    const searchQuery = new SearchQuery({
      query: query.trim(),
      category: category.trim(),
      location: location?.trim() || null,
      page: pageNumber,
      userId: session.user.id
    });

    await searchQuery.save();

    // Zapisz linki tylko dla nowych domen
    const savedLinks = [];
    const newDomains = new Map<string, { protocol: string; domain: string; count: number }>();
    const skippedLinks = [];

    for (const result of serperData.organic || []) {
      // Sprawdź domenę przed dodaniem linku
      try {
        const url = new URL(result.link);
        const protocol = url.protocol.replace(':', '');
        const domain = url.hostname;
        const fullDomain = `${protocol}://${domain}`;

        // Sprawdź czy domena już istnieje w systemie dla tego użytkownika (globalnie)
        const existingDomain = await Domain.findOne({
          userId: session.user.id,
          fullDomain
        });

        if (existingDomain) {
          // Domena już istnieje - pomiń ten link
          skippedLinks.push({
            url: result.link,
            title: result.title,
            reason: `Domena już istnieje w kategorii "${existingDomain.category}"`
          });
          continue;
        }

        // Domena nie istnieje - dodaj link
        const link = new Link({
          url: result.link,
          title: result.title,
          snippet: result.snippet,
          category: category.trim(),
          query: query.trim(),
          location: location?.trim() || null,
          userId: session.user.id,
          searchQueryId: searchQuery._id
        });

        await link.save();
        savedLinks.push(link);

        // Dodaj domenę do listy nowych domen
        if (newDomains.has(fullDomain)) {
          newDomains.get(fullDomain)!.count++;
        } else {
          newDomains.set(fullDomain, { protocol, domain, count: 1 });
        }
      } catch (error) {
        console.error('Błąd parsowania URL:', result.link, error);
        skippedLinks.push({
          url: result.link,
          title: result.title,
          reason: 'Błąd parsowania URL'
        });
      }
    }

    // Zapisz nowe domeny (tylko te, które nie istniały wcześniej) z sprawdzaniem CMS
    for (const [fullDomain, domainInfo] of newDomains) {
      try {
        // Sprawdź CMS dla nowej domeny
        const cmsCheckResult = await checkDomainCms(fullDomain);

        const domain = new Domain({
          domain: domainInfo.domain,
          protocol: domainInfo.protocol,
          fullDomain,
          category: category.trim(),
          userId: session.user.id,
          linkCount: domainInfo.count,
          cms: cmsCheckResult.cms
        });
        await domain.save();
      } catch (error) {
        console.error('Błąd zapisywania nowej domeny:', fullDomain, error);
      }
    }



    return NextResponse.json({
      success: true,
      searchQuery: {
        id: searchQuery._id,
        query: searchQuery.query,
        category: searchQuery.category,
        location: searchQuery.location,
        page: searchQuery.page
      },
      linksCount: savedLinks.length,
      domainsCount: newDomains.size,
      skippedCount: skippedLinks.length,
      results: savedLinks.map(link => ({
        id: link._id,
        url: link.url,
        title: link.title,
        snippet: link.snippet
      })),
      skippedLinks: skippedLinks.map(skipped => ({
        url: skipped.url,
        title: skipped.title,
        reason: skipped.reason
      }))
    });

  } catch (error) {
    console.error('Błąd wyszukiwania:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas wyszukiwania' },
      { status: 500 }
    );
  }
}
