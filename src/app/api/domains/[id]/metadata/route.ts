import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';

// GET endpoint do pobierania metadata domeny
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { id } = await params;

    // Sprawdź czy domena istnieje i należy do użytkownika
    const domain = await Domain.findOne({
      _id: id,
      userId: session.user.id
    });

    if (!domain) {
      return NextResponse.json(
        { error: 'Domena nie została znaleziona' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      metadata: domain.metadata || {}
    });

  } catch (error) {
    console.error('Błąd pobierania metadata domeny:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas pobierania metadata domeny' },
      { status: 500 }
    );
  }
}

// POST endpoint do dodawania/aktualizacji metadata domeny
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { key, value } = body;

    if (!key || typeof key !== 'string') {
      return NextResponse.json(
        { error: 'Klucz jest wymagany i musi być stringiem' },
        { status: 400 }
      );
    }

    if (value === undefined) {
      return NextResponse.json(
        { error: 'Wartość jest wymagana' },
        { status: 400 }
      );
    }

    await connectDB();

    const { id } = await params;

    // Sprawdź czy domena istnieje i należy do użytkownika
    const domain = await Domain.findOne({
      _id: id,
      userId: session.user.id
    });

    if (!domain) {
      return NextResponse.json(
        { error: 'Domena nie została znaleziona' },
        { status: 404 }
      );
    }

    // Aktualizuj metadata
    const currentMetadata = domain.metadata || {};
    currentMetadata[key] = value;

    await Domain.findByIdAndUpdate(
      id,
      {
        metadata: currentMetadata,
        updatedAt: new Date()
      },
      { new: true }
    );

    return NextResponse.json({
      success: true,
      message: 'Metadata zostało zaktualizowane',
      metadata: currentMetadata
    });

  } catch (error) {
    console.error('Błąd aktualizacji metadata domeny:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas aktualizacji metadata domeny' },
      { status: 500 }
    );
  }
}

// DELETE endpoint do usuwania klucza z metadata
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const key = searchParams.get('key');

    if (!key) {
      return NextResponse.json(
        { error: 'Klucz jest wymagany' },
        { status: 400 }
      );
    }

    await connectDB();

    const { id } = await params;

    // Sprawdź czy domena istnieje i należy do użytkownika
    const domain = await Domain.findOne({
      _id: id,
      userId: session.user.id
    });

    if (!domain) {
      return NextResponse.json(
        { error: 'Domena nie została znaleziona' },
        { status: 404 }
      );
    }

    // Usuń klucz z metadata
    const currentMetadata = domain.metadata || {};
    delete currentMetadata[key];

    await Domain.findByIdAndUpdate(
      id,
      {
        metadata: currentMetadata,
        updatedAt: new Date()
      },
      { new: true }
    );

    return NextResponse.json({
      success: true,
      message: 'Klucz metadata został usunięty',
      metadata: currentMetadata
    });

  } catch (error) {
    console.error('Błąd usuwania metadata domeny:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas usuwania metadata domeny' },
      { status: 500 }
    );
  }
}

// PATCH endpoint do aktualizacji całego obiektu metadata
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { metadata } = body;

    if (!metadata || typeof metadata !== 'object') {
      return NextResponse.json(
        { error: 'Metadata musi być obiektem' },
        { status: 400 }
      );
    }

    await connectDB();

    const { id } = await params;

    // Sprawdź czy domena istnieje i należy do użytkownika
    const domain = await Domain.findOne({
      _id: id,
      userId: session.user.id
    });

    if (!domain) {
      return NextResponse.json(
        { error: 'Domena nie została znaleziona' },
        { status: 404 }
      );
    }

    // Zastąp całe metadata
    await Domain.findByIdAndUpdate(
      id,
      {
        metadata: metadata,
        updatedAt: new Date()
      },
      { new: true }
    );

    return NextResponse.json({
      success: true,
      message: 'Metadata zostało zaktualizowane',
      metadata: metadata
    });

  } catch (error) {
    console.error('Błąd aktualizacji metadata domeny:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas aktualizacji metadata domeny' },
      { status: 500 }
    );
  }
}
