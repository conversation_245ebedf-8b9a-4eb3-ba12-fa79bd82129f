"use client";

import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import RegisterForm from "@/components/auth/RegisterForm";

export default function RegisterPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    // Jeśli użytkownik jest już zalogowany, przekieruj na dashboard
    if (status === "authenticated" && session) {
      router.push("/dashboard");
    }
  }, [session, status, router]);

  // Pokaż loading podczas sprawdzania sesji
  if (status === "loading") {
    return (
      <div className="bg-muted flex min-h-svh flex-col items-center justify-center p-6 md:p-10">
        <div className="w-full max-w-sm text-center">
          <div className="flex justify-center mb-4">
            <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-2xl">
                AI
              </span>
            </div>
          </div>
          <h1 className="text-3xl font-bold text-foreground">AI Studio</h1>
          <p className="text-muted-foreground mt-2">Ładowanie...</p>
        </div>
      </div>
    );
  }

  // Jeśli użytkownik jest zalogowany, nie pokazuj formularza rejestracji
  if (status === "authenticated") {
    return null;
  }

  return (
    <div className="bg-muted flex min-h-svh flex-col items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm md:max-w-3xl">
        <RegisterForm />
      </div>
    </div>
  );
}
