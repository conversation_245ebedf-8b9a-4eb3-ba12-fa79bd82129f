RELACJE MIĘDZY ELEMENTAMI - PASEK BOCZNY Z NOWYMI DOMENAMI WORDPRESS
====================================================================

OPIS FUNKCJONALNOŚCI:
W szczegółach domeny po prawej stronie dodano stały pasek z listą domen ze statusem "nowy" i CMS WordPress. Pasek zajmuje pełną wysokość ekranu i ma position sticky.

KOMPONENTY I PLIKI:
===================

1. GŁÓWNY KOMPONENT:
   - src/components/domain/RelatedDomainsSidebar.tsx
   - Nowy komponent React wyświetlający listę powiązanych domen
   - Używa shadcn/ui komponentów (Card, ScrollArea, Badge, Button)

2. ZMODYFIKOWANE PLIKI:
   - src/app/dashboard/domains/[id]/page.tsx
   - Dodano import RelatedDomainsSidebar
   - Zmieniono layout z 2-kolumnowego na 3-kolumnowy
   - Dodano pasek boczny po prawej stronie

3. DOKUMENTACJA:
   - README.md - dodano opis nowej funkcjonalności
   - docs/related-domains-sidebar-relations.txt - ten plik

RELACJE MIĘDZY KOMPONENTAMI:
============================

RelatedDomainsSidebar
├── Otrzymuje props: currentDomain (Domain interface)
├── Używa API: GET /api/domains z filtrami status="new" i cms="wordpress"
├── Filtruje wyniki: usuwa aktualną domenę z listy
├── Wyświetla: maksymalnie 20 nowych domen WordPress
├── Design: pełna wysokość ekranu (sidebar-height) z position sticky
└── Nawigacja: linki do szczegółów domen

DomainDetailsPage
├── Importuje: RelatedDomainsSidebar
├── Layout: grid grid-cols-1 xl:grid-cols-[1fr_300px]
├── Przekazuje: domain object jako currentDomain prop
└── Responsywność: pasek ukryty poniżej breakpoint XL

PRZEPŁYW DANYCH:
================

1. Użytkownik otwiera szczegóły domeny (/dashboard/domains/[id])
2. DomainDetailsPage pobiera dane domeny z API
3. RelatedDomainsSidebar otrzymuje currentDomain jako prop
4. Komponent wykonuje zapytanie do /api/domains z filtrami:
   - status: "new" (zawsze nowe domeny)
   - cms: "wordpress" (tylko domeny WordPress)
   - limit: 20
5. Filtruje wyniki usuwając aktualną domenę
6. Wyświetla listę nowych domen WordPress z informacjami:
   - Nazwa domeny (link do szczegółów)
   - Liczba linków
   - CMS (badge)
   - Ocena (badge z kolorami)
   - Przyciski akcji (zewnętrzny link, szczegóły)

API ENDPOINTS UŻYWANE:
======================

GET /api/domains
- Parametry: status="new", cms="wordpress", limit=20
- Zwraca: lista nowych domen WordPress
- Używany przez RelatedDomainsSidebar do pobierania nowych domen WordPress

STYLE I RESPONSYWNOŚĆ:
======================

- Pasek boczny: szerokość 300px na ekranach XL+
- Pełna wysokość: h-screen z position sticky top-4
- Ukryty na mniejszych ekranach: hidden xl:block
- ScrollArea: flex-1 dla elastycznej wysokości
- Ulepszone odstępy: p-4 dla elementów, space-y-3 między domenami
- Hover effects: opacity transitions dla przycisków akcji
- Badge colors: różne kolory dla statusów i ocen
- Flexbox layout: flex flex-col dla pionowego układu

FUNKCJONALNOŚCI:
================

1. AUTOMATYCZNE FILTROWANIE:
   - Zawsze domeny ze statusem "new" (nowe)
   - Tylko domeny z CMS WordPress
   - Wykluczenie aktualnej domeny z wyników
   - Limit 20 domen dla lepszej wydajności

2. WYŚWIETLANIE INFORMACJI:
   - Nazwa domeny (skrócona jeśli za długa)
   - Liczba linków
   - Typ CMS (WordPress/inny)
   - Ocena domeny (wysoka/niska/brak)

3. NAWIGACJA:
   - Link do szczegółów domeny
   - Link do pełnej listy nowych domen WordPress

4. STAN ŁADOWANIA:
   - Wyświetlanie "Ładowanie..." podczas pobierania danych
   - Komunikat gdy brak nowych domen WordPress

5. STICKY POSITIONING:
   - Position sticky z top-4 dla stałej widoczności
   - Pasek pozostaje widoczny podczas przewijania strony
   - Pełna wysokość ekranu dla maksymalnego wykorzystania przestrzeni

INTEGRACJA Z ISTNIEJĄCYM KODEM:
===============================

- Wykorzystuje istniejące API /api/domains
- Używa istniejących interfejsów Domain
- Kompatybilny z systemem autoryzacji
- Używa shadcn/ui komponentów z projektu
- Respektuje istniejące style i motywy

PRZYSZŁE ROZSZERZENIA:
======================

- Możliwość zmiany liczby wyświetlanych domen
- Dodatkowe filtry (CMS, ocena)
- Sortowanie powiązanych domen
- Cache dla lepszej wydajności
- Infinite scroll dla większej liczby domen
