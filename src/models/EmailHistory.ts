import mongoose, { Document, Schema } from 'mongoose';

export interface IEmailHistory extends Document {
  domainId: string;
  recipientEmail: string;
  subject: string;
  emailType: 'audit' | 'notification' | 'other';
  status: 'sent' | 'failed' | 'pending';
  sentAt: Date;
  userId: string;
  emailContent?: string; // Pełna treść wysłanego maila (HTML)
  metadata?: {
    messageId?: string;
    errorMessage?: string;
    auditContentLength?: number;
    [key: string]: string | number | boolean | Date | null | undefined;
  };
  createdAt: Date;
  updatedAt: Date;
}

const EmailHistorySchema: Schema = new Schema({
  domainId: {
    type: String,
    required: [true, 'ID domeny jest wymagane'],
    ref: 'Domain'
  },
  recipientEmail: {
    type: String,
    required: [true, 'Email odbiorcy jest wymagany'],
    trim: true,
    lowercase: true,
    maxlength: [255, 'Email odbiorcy nie może być dłuższy niż 255 znaków']
  },
  subject: {
    type: String,
    required: [true, 'Temat wiadomości jest wymagany'],
    trim: true,
    maxlength: [500, 'Temat wiadomości nie może być dłuższy niż 500 znaków']
  },
  emailType: {
    type: String,
    required: [true, 'Typ emaila jest wymagany'],
    enum: {
      values: ['audit', 'notification', 'other'],
      message: 'Typ emaila musi być jednym z: audit, notification, other'
    },
    default: 'audit'
  },
  status: {
    type: String,
    required: [true, 'Status wysyłki jest wymagany'],
    enum: {
      values: ['sent', 'failed', 'pending'],
      message: 'Status musi być jednym z: sent, failed, pending'
    },
    default: 'pending'
  },
  sentAt: {
    type: Date,
    required: [true, 'Data wysyłki jest wymagana'],
    default: Date.now
  },
  userId: {
    type: String,
    required: [true, 'ID użytkownika jest wymagane']
  },
  emailContent: {
    type: String,
    required: false,
    maxlength: [50000, 'Treść emaila nie może być dłuższa niż 50000 znaków']
  },
  metadata: {
    messageId: { type: String },
    errorMessage: { type: String },
    auditContentLength: { type: Number },
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Indeks złożony dla szybszego wyszukiwania historii maili domeny
EmailHistorySchema.index({ domainId: 1, sentAt: -1 });

// Indeks dla wyszukiwania historii maili użytkownika
EmailHistorySchema.index({ userId: 1, sentAt: -1 });

// Indeks dla wyszukiwania po statusie
EmailHistorySchema.index({ status: 1, sentAt: -1 });

// Indeks dla wyszukiwania po typie emaila
EmailHistorySchema.index({ emailType: 1, sentAt: -1 });

// Indeks dla wyszukiwania po odbiorcy
EmailHistorySchema.index({ recipientEmail: 1, sentAt: -1 });

export default mongoose.models.EmailHistory || mongoose.model<IEmailHistory>('EmailHistory', EmailHistorySchema);
