import mongoose, { Document, Schema } from 'mongoose';

export interface ICategory extends Document {
  name: string;
  description?: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

const CategorySchema: Schema = new Schema({
  name: {
    type: String,
    required: [true, 'Nazwa kategorii jest wymagana'],
    trim: true,
    maxlength: [100, 'Nazwa kategorii nie może być dłuższa niż 100 znaków']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Opis kategorii nie może być dłuższy niż 500 znaków']
  },
  userId: {
    type: String,
    required: [true, 'ID użytkownika jest wymagane']
  }
}, {
  timestamps: true
});

// Indeks złożony dla unikalności nazwy kategorii dla użytkownika
CategorySchema.index({ userId: 1, name: 1 }, { unique: true });

export default mongoose.models.Category || mongoose.model<ICategory>('Category', CategorySchema);
