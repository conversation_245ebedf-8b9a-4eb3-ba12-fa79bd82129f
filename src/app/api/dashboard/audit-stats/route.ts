import { NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';
import { getServerSession } from "next-auth/next";
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET() {
  const session = (await getServerSession(authOptions)) as Session | null;

  if (!session || !session.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    await connectDB();

    // Oblicz daty dla ostatnich 30 dni
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 29); // 29 dni wstecz + dzisiaj = 30 dni

    // Ustaw godziny na początek i koniec dnia
    const startDate = new Date(thirtyDaysAgo);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date(today);
    endDate.setHours(23, 59, 59, 999);

    // Agregacja danych audytów z ostatnich 7 dni
    const auditStats = await Domain.aggregate([
      {
        $match: {
          userId: session.user.id,
          'metadata.hasAudit': true,
          'metadata.dateAudit': {
            $exists: true,
            $ne: null
          }
        }
      },
      {
        $addFields: {
          auditDate: {
            $dateFromString: {
              dateString: '$metadata.dateAudit',
              onError: null
            }
          }
        }
      },
      {
        $match: {
          auditDate: {
            $gte: startDate,
            $lte: endDate
          }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$auditDate'
            }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    // Przygotuj dane dla wszystkich 30 dni (wypełnij brakujące dni zerami)
    const chartData = [];
    const monthNames = ['Sty', 'Lut', 'Mar', 'Kwi', 'Maj', 'Cze', 'Lip', 'Sie', 'Wrz', 'Paź', 'Lis', 'Gru'];

    for (let i = 29; i >= 0; i--) {
      const currentDate = new Date(today);
      currentDate.setDate(today.getDate() - i);
      const dateString = currentDate.toISOString().split('T')[0];

      // Znajdź dane dla tego dnia
      const dayData = auditStats.find(stat => stat._id === dateString);
      const count = dayData ? dayData.count : 0;

      // Formatuj etykietę daty (tylko dzień i miesiąc dla lepszej czytelności)
      const day = currentDate.getDate();
      const month = monthNames[currentDate.getMonth()];

      chartData.push({
        date: dateString,
        label: `${day} ${month}`,
        audits: count
      });
    }

    // Oblicz łączną liczbę audytów z ostatnich 30 dni
    const totalAudits = chartData.reduce((sum, day) => sum + day.audits, 0);

    return NextResponse.json({
      success: true,
      data: chartData,
      summary: {
        totalAudits,
        period: '30 dni',
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      }
    });

  } catch (error) {
    console.error('Error fetching audit stats:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
