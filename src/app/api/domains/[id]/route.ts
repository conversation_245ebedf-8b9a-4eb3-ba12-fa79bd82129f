import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next'; // Changed import path
import { Session } from 'next-auth'; // Added Session import
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';
import { sendAcceptedDomainToWebhook } from '@/lib/webhook';
import Link from '@/models/Link';
import Category from '@/models/Category';

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null; // Explicitly cast session

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { id } = await params;

    // Pobierz domenę
    const domain = await Domain.findOne({
      _id: id,
      userId: session.user.id
    });

    if (!domain) {
      return NextResponse.json(
        { error: 'Domena nie została znaleziona' },
        { status: 404 }
      );
    }

    // Pobierz linki związane z domeną
    const links = await Link.find({
      userId: session.user.id,
      url: { $regex: `^${domain.protocol}://${domain.domain}`, $options: 'i' }
    })
    .sort({ createdAt: -1 })
    .limit(10); // Ostatnie 10 linków

    // Pobierz statystyki domeny
    const linkStats = await Link.aggregate([
      {
        $match: {
          userId: session.user.id,
          url: { $regex: `^${domain.protocol}://${domain.domain}`, $options: 'i' }
        }
      },
      {
        $group: {
          _id: null,
          totalLinks: { $sum: 1 },
          categories: { $addToSet: '$category' }
        }
      }
    ]);

    const stats = linkStats[0] || { totalLinks: 0, categories: [] };

    return NextResponse.json({
      success: true,
      domain: {
        id: domain._id,
        domain: domain.domain,
        protocol: domain.protocol,
        fullDomain: domain.fullDomain,
        category: domain.category,
        linkCount: domain.linkCount,
        status: domain.status,
        cms: domain.cms,
        ocena: domain.ocena,
        secureAudit: !!(domain.auditContent && domain.auditContent.trim().length > 0),
        contact_data: domain.contact_data || {},
        metadata: domain.metadata || {},
        createdAt: domain.createdAt,
        updatedAt: domain.updatedAt
      },
      links: links.map(link => ({
        id: link._id,
        url: link.url,
        title: link.title,
        snippet: link.snippet,
        category: link.category,
        createdAt: link.createdAt
      })),
      stats: {
        totalLinks: stats.totalLinks,
        categories: stats.categories
      }
    });

  } catch (error) {
    console.error('Błąd pobierania szczegółów domeny:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas pobierania szczegółów domeny' },
      { status: 500 }
    );
  }
}

// PATCH endpoint do aktualizacji właściwości domeny (status, CMS, itp.)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null; // Explicitly cast session

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const requestBody = await request.json();
    const { status, cms, ocena, isRejected, category } = requestBody;

    // Walidacja danych
    if (status && !['new', 'accepted', 'rejected', 'closed', 'in_progress', 'production', 'hold'].includes(status)) {
      return NextResponse.json(
        { error: 'status musi być jedną z wartości: new, accepted, rejected, closed, in_progress, production, hold' },
        { status: 400 }
      );
    }

    if (ocena && !['wysoka', 'niska', 'brak'].includes(ocena)) {
      return NextResponse.json(
        { error: 'ocena musi być jedną z wartości: wysoka, niska, brak' },
        { status: 400 }
      );
    }

    if (isRejected !== undefined && typeof isRejected !== 'boolean') {
      return NextResponse.json(
        { error: 'isRejected musi być wartością boolean' },
        { status: 400 }
      );
    }

    if (cms !== undefined && cms !== null && typeof cms !== 'string') {
      return NextResponse.json(
        { error: 'cms musi być stringiem lub null' },
        { status: 400 }
      );
    }

    if (cms !== undefined && cms !== null && !['wordpress', 'inny'].includes(cms)) {
      return NextResponse.json(
        { error: 'cms musi być wartością "wordpress", "inny" lub null' },
        { status: 400 }
      );
    }

    if (category !== undefined && (typeof category !== 'string' || category.trim().length === 0)) {
      return NextResponse.json(
        { error: 'Kategoria musi być niepustym stringiem' },
        { status: 400 }
      );
    }

    if (category !== undefined && category.trim().length > 100) {
      return NextResponse.json(
        { error: 'Kategoria nie może być dłuższa niż 100 znaków' },
        { status: 400 }
      );
    }

    await connectDB();

    const { id } = await params;

    // Sprawdź czy domena istnieje i należy do użytkownika
    const domain = await Domain.findOne({
      _id: id,
      userId: session.user.id
    });

    if (!domain) {
      return NextResponse.json(
        { error: 'Domena nie została znaleziona' },
        { status: 404 }
      );
    }

    // Jeśli kategoria jest podana, sprawdź czy istnieje i należy do użytkownika
    if (category !== undefined) {
      const categoryExists = await Category.findOne({
        name: category.trim(),
        userId: session.user.id
      });

      if (!categoryExists) {
        return NextResponse.json(
          { error: 'Podana kategoria nie istnieje lub nie należy do użytkownika' },
          { status: 400 }
        );
      }
    }

    // Przygotuj obiekt aktualizacji
    const updateObject: Record<string, string | boolean | null | undefined> = {};
    let message = '';

    if (status) {
      updateObject.status = status;
      const statusMessages = {
        'new': 'Status domeny został zmieniony na: nowa',
        'accepted': 'Domena została zaakceptowana',
        'rejected': 'Domena została odrzucona',
        'closed': 'Domena została zamknięta',
        'in_progress': 'Domena została oznaczona jako w trakcie',
        'production': 'Domena została oznaczona jako produkcyjna',
        'hold': 'Domena została wstrzymana'
      };
      message = statusMessages[status as keyof typeof statusMessages];
    }

    if (isRejected !== undefined) {
      updateObject.isRejected = isRejected;
      if (message) {
        message += isRejected ? ' i została oznaczona jako odrzucona' : ' i została przywrócona';
      } else {
        message = isRejected ? 'Domena została oznaczona jako odrzucona' : 'Domena została przywrócona';
      }
    }

    if (cms !== undefined) {
      updateObject.cms = cms;
      if (message) {
        message += ' i CMS został zaktualizowany';
      } else {
        message = 'CMS został zaktualizowany';
      }
    }

    if (ocena !== undefined) {
      updateObject.ocena = ocena;
      if (message) {
        message += ' i ocena została zaktualizowana';
      } else {
        message = 'Ocena została zaktualizowana';
      }
    }

    if (category !== undefined) {
      updateObject.category = category.trim();
      if (message) {
        message += ' i kategoria została zaktualizowana';
      } else {
        message = 'Kategoria została zaktualizowana';
      }
    }

    // Aktualizuj domenę
    const updatedDomain = await Domain.findByIdAndUpdate(
      id,
      updateObject,
      { new: true }
    );

    // Jeśli domena została zaakceptowana, wyślij dane na webhook
    if (status === 'accepted' && updatedDomain) {
      try {
        await sendAcceptedDomainToWebhook(updatedDomain);
      } catch (webhookError) {
        console.error('Błąd wysyłania na webhook:', webhookError);
        // Nie przerywamy procesu - domena została zaakceptowana, ale webhook się nie udał
      }
    }

    return NextResponse.json({
      success: true,
      message,
      domain: {
        id: updatedDomain._id,
        domain: updatedDomain.domain,
        protocol: updatedDomain.protocol,
        fullDomain: updatedDomain.fullDomain,
        category: updatedDomain.category,
        linkCount: updatedDomain.linkCount,
        status: updatedDomain.status,
        cms: updatedDomain.cms,
        ocena: updatedDomain.ocena,
        auditContent: updatedDomain.auditContent,
        lighthouseContent: updatedDomain.lighthouseContent,
        secureAudit: updatedDomain.secureAudit,
        contact_data: updatedDomain.contact_data || {},
        createdAt: updatedDomain.createdAt,
        updatedAt: updatedDomain.updatedAt
      }
    });

  } catch (error) {
    console.error('Błąd aktualizacji domeny:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas aktualizacji domeny' },
      { status: 500 }
    );
  }
}

// DELETE endpoint do usuwania pojedynczej domeny
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { id } = await params;

    // Sprawdź czy domena istnieje i należy do użytkownika
    const domain = await Domain.findOne({
      _id: id,
      userId: session.user.id
    });

    if (!domain) {
      return NextResponse.json(
        { error: 'Domena nie została znaleziona' },
        { status: 404 }
      );
    }

    // Usuń wszystkie linki związane z domeną
    const deletedLinksResult = await Link.deleteMany({
      userId: session.user.id,
      url: { $regex: `^${domain.protocol}://${domain.domain}`, $options: 'i' }
    });

    // Usuń domenę
    await Domain.findByIdAndDelete(id);

    return NextResponse.json({
      success: true,
      message: `Domena ${domain.domain} została usunięta wraz z ${deletedLinksResult.deletedCount} powiązanymi linkami`,
      deletedDomain: {
        id: domain._id,
        domain: domain.domain,
        fullDomain: domain.fullDomain,
        category: domain.category
      },
      deletedLinksCount: deletedLinksResult.deletedCount
    });

  } catch (error) {
    console.error('Błąd usuwania domeny:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas usuwania domeny' },
      { status: 500 }
    );
  }
}
