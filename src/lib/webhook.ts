import { IDomain } from '@/models/Domain';
import Domain from '@/models/Domain';

/**
 * Interfejs dla użytkownika WordPress
 */
interface WordPressUser {
  slug: string;
  name: string;
}

/**
 * Pobiera listę użytkowników z WordPress JSON API
 * @param domain - Obiekt domeny
 * @returns Promise<WordPressUser[] | null> - lista użytkowników lub null jeśli się nie udało
 */
async function fetchWordPressUsers(domain: IDomain): Promise<WordPressUser[] | null> {
  try {
    const wpApiUrl = `${domain.fullDomain}/wp-json/wp/v2/users`;
    console.log('Próba pobrania użytkowników WordPress z:', wpApiUrl);

    const response = await fetch(wpApiUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'AI-Studio-Domain-Checker/1.0'
      },
      // Timeout po 10 sekundach
      signal: AbortSignal.timeout(10000)
    });

    if (!response.ok) {
      console.log(`Nie udało się pobrać użytkowników WordPress (${response.status}): ${response.statusText}`);
      return null;
    }

    const users = await response.json();

    if (!Array.isArray(users)) {
      console.log('Odpowiedź z WordPress API nie jest tablicą');
      return null;
    }

    // Mapuj użytkowników do naszego formatu
    const mappedUsers: WordPressUser[] = users.map(user => ({
      slug: user.slug || '',
      name: user.name || ''
    })).filter(user => user.slug && user.name); // Filtruj tylko użytkowników z slug i name

    console.log(`Pobrano ${mappedUsers.length} użytkowników WordPress`);
    return mappedUsers;

  } catch (error) {
    console.log('Błąd podczas pobierania użytkowników WordPress:', error);
    return null;
  }
}

/**
 * Wysyła dane zaakceptowanej domeny na webhook
 * @param domain - Obiekt domeny do wysłania
 * @returns Promise<boolean> - true jeśli wysłano pomyślnie, false w przeciwnym razie
 */
export async function sendAcceptedDomainToWebhook(domain: IDomain): Promise<boolean> {
  const webhookUrl = process.env.WEBHOOK_ACCEPTED_DOMAIN_URL;

  if (!webhookUrl) {
    console.warn('WEBHOOK_ACCEPTED_DOMAIN_URL nie jest skonfigurowany');
    return false;
  }

  try {
    // Spróbuj pobrać użytkowników WordPress jeśli domena ma CMS ustawiony na 'wordpress'
    const updatedMetadata = { ...domain.metadata };

    if (domain.cms === 'wordpress') {
      console.log('Domena ma CMS WordPress, próbuję pobrać użytkowników...');
      const wpUsers = await fetchWordPressUsers(domain);

      if (wpUsers && wpUsers.length > 0) {
        // Udało się pobrać użytkowników
        updatedMetadata.wpUsers = wpUsers;
        updatedMetadata.gotUsers = true;
        console.log(`Pobrano ${wpUsers.length} użytkowników WordPress dla domeny ${domain.domain}`);
      } else {
        // Nie udało się pobrać użytkowników
        updatedMetadata.gotUsers = false;
        console.log(`Nie udało się pobrać użytkowników WordPress dla domeny ${domain.domain}`);
      }

      // Aktualizuj domenę w bazie danych z nowymi metadata
      try {
        await Domain.findByIdAndUpdate(
          domain._id,
          {
            $set: {
              metadata: updatedMetadata,
              updatedAt: new Date()
            }
          }
        );
        console.log('Zaktualizowano metadata domeny w bazie danych');
      } catch (dbError) {
        console.error('Błąd podczas aktualizacji metadata domeny:', dbError);
        // Kontynuuj mimo błędu aktualizacji bazy danych
      }
    } else {
      console.log('Domena nie ma CMS WordPress, pomijam pobieranie użytkowników');
    }

    // Przygotuj dane w formacie zgodnym z /api/domains/to-audit
    const domainData = {
      id: domain._id?.toString() || domain.id,
      domain: domain.domain,
      protocol: domain.protocol,
      fullDomain: domain.fullDomain,
      category: domain.category,
      cms: domain.cms,
      status: domain.status,
      linkCount: domain.linkCount,
      auditContent: domain.auditContent,
      lighthouseContent: domain.lighthouseContent,
      metadata: updatedMetadata, // Użyj zaktualizowanych metadata
      createdAt: domain.createdAt,
      updatedAt: new Date() // Aktualna data
    };

    // Przygotuj payload w formacie zgodnym z API /api/domains/to-audit
    const payload = {
      success: true,
      domains: [domainData],
      count: 1,
      totalCount: 1,
      message: 'Zaakceptowana domena wysłana na webhook'
    };

    console.log('Wysyłanie zaakceptowanej domeny na webhook:', webhookUrl);
    console.log('Dane domeny:', domainData);

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      console.error('Błąd wysyłania na webhook:', response.status, response.statusText);
      return false;
    }

    console.log('Pomyślnie wysłano zaakceptowaną domenę na webhook');
    return true;
  } catch (error) {
    console.error('Błąd podczas wysyłania na webhook:', error);
    return false;
  }
}
