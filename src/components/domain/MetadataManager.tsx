"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import {
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  Database,
  Key,
  FileText,
  Zap,
  Server,
  Globe,
  Shield,
  Settings,
  Tag,
  Star,
  CheckCircle,
  AlertCircle,
  Code,
  Lock,
  Heart,
  Home,
  User,
  Users,
  Calendar,
  Image,
  Search,
  Bell,
  MessageCircle,
  Phone,
  MapPin,
  Link,
  TrendingUp,
  BarChart,
  Activity,
  Target,
  Award,
  ShoppingCart,
  CreditCard,
  DollarSign,
  Briefcase,
  Building,
  Truck,
  Coffee,
  Lightbulb,
  Sun,
  Moon,
  Cloud,
  Mail,
} from "lucide-react";

interface MetadataManagerProps {
  domainId: string;
  initialMetadata?: Record<string, string | number | boolean | null>;
  onMetadataUpdate?: (metadata: Record<string, string | number | boolean | null>) => void;
}

interface MetadataItem {
  key: string;
  value: string | number | boolean | null;
  type: string;
}

interface MetadataButton {
  id: string;
  label: string;
  key: string;
  value: string | number | boolean;
  icon: string;
  description?: string;
  order: number;
}

export default function MetadataManager({
  domainId,
  initialMetadata = {},
  onMetadataUpdate,
}: MetadataManagerProps) {
  const [metadata, setMetadata] =
    useState<Record<string, string | number | boolean | null>>(initialMetadata);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<MetadataItem | null>(null);
  const [newKey, setNewKey] = useState("");
  const [newValue, setNewValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [metadataButtons, setMetadataButtons] = useState<MetadataButton[]>([]);
  const [isLoadingButtons, setIsLoadingButtons] = useState(true);

  // Pobierz przyciski metadata
  useEffect(() => {
    const fetchMetadataButtons = async () => {
      try {
        const response = await fetch("/api/settings/metadata-buttons");
        if (response.ok) {
          const data = await response.json();
          setMetadataButtons(data.metadataButtons || []);
        }
      } catch (error) {
        console.error("Błąd pobierania przycisków metadata:", error);
      } finally {
        setIsLoadingButtons(false);
      }
    };

    fetchMetadataButtons();
  }, []);

  // Funkcja do określenia typu wartości
  const getValueType = (value: string | number | boolean | null): string => {
    if (value === null) return "null";
    if (Array.isArray(value)) return "array";
    return typeof value;
  };

  // Funkcja do formatowania wartości do wyświetlenia
  const formatValue = (value: string | number | boolean | null): string => {
    if (value === null || value === undefined) return "null";
    if (typeof value === "object") return JSON.stringify(value);
    return String(value);
  };

  // Funkcja do parsowania wartości z inputa
  const parseValue = (value: string, type: string): string | number | boolean | null => {
    if (value === "null" || value === "") return null;

    try {
      switch (type) {
        case "number":
          const num = Number(value);
          return isNaN(num) ? value : num;
        case "boolean":
          return value.toLowerCase() === "true";
        case "object":
        case "array":
          return JSON.parse(value);
        default:
          return value;
      }
    } catch {
      return value;
    }
  };

  // Dodaj lub zaktualizuj metadata
  const handleSaveMetadata = async () => {
    if (!newKey.trim()) {
      toast.error("Klucz jest wymagany");
      return;
    }

    setIsLoading(true);
    try {
      const parsedValue = parseValue(newValue, getValueType(newValue));

      const response = await fetch(`/api/domains/${domainId}/metadata`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          key: newKey.trim(),
          value: parsedValue,
        }),
      });

      if (!response.ok) {
        throw new Error("Błąd zapisywania metadata");
      }

      const data = await response.json();

      if (data.success) {
        setMetadata(data.metadata);
        onMetadataUpdate?.(data.metadata);
        toast.success("Metadata zostało zapisane");
        setIsDialogOpen(false);
        setNewKey("");
        setNewValue("");
        setEditingItem(null);
      } else {
        throw new Error(data.error || "Błąd zapisywania metadata");
      }
    } catch (error) {
      console.error("Błąd zapisywania metadata:", error);
      toast.error("Nie udało się zapisać metadata");
    } finally {
      setIsLoading(false);
    }
  };

  // Usuń klucz metadata
  const handleDeleteMetadata = async (key: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/domains/${domainId}/metadata?key=${encodeURIComponent(key)}`,
        {
          method: "DELETE",
        }
      );

      if (!response.ok) {
        throw new Error("Błąd usuwania metadata");
      }

      const data = await response.json();

      if (data.success) {
        setMetadata(data.metadata);
        onMetadataUpdate?.(data.metadata);
        toast.success("Klucz metadata został usunięty");
      } else {
        throw new Error(data.error || "Błąd usuwania metadata");
      }
    } catch (error) {
      console.error("Błąd usuwania metadata:", error);
      toast.error("Nie udało się usunąć metadata");
    } finally {
      setIsLoading(false);
    }
  };

  // Rozpocznij edycję
  const startEdit = (key: string, value: string | number | boolean | null) => {
    setEditingItem({
      key,
      value,
      type: getValueType(value),
    });
    setNewKey(key);
    setNewValue(formatValue(value));
    setIsDialogOpen(true);
  };

  // Rozpocznij dodawanie nowego
  const startAdd = () => {
    setEditingItem(null);
    setNewKey("");
    setNewValue("");
    setIsDialogOpen(true);
  };

  // Szybkie dodanie metadata z przycisku
  const handleQuickAddMetadata = async (button: MetadataButton) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/domains/${domainId}/metadata`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          key: button.key,
          value: button.value,
        }),
      });

      if (!response.ok) {
        throw new Error("Błąd zapisywania metadata");
      }

      const data = await response.json();

      if (data.success) {
        setMetadata(data.metadata);
        onMetadataUpdate?.(data.metadata);
        toast.success(`Dodano metadata: ${button.label}`);
      } else {
        throw new Error(data.error || "Błąd zapisywania metadata");
      }
    } catch (error) {
      console.error("Błąd zapisywania metadata:", error);
      toast.error("Nie udało się dodać metadata");
    } finally {
      setIsLoading(false);
    }
  };

  const metadataEntries = Object.entries(metadata);

  // Funkcja do renderowania ikon
  const renderIcon = (iconName: string) => {
    const iconProps = { className: "h-4 w-4" };

    switch (iconName) {
      case "Server":
        return <Server {...iconProps} />;
      case "Database":
        return <Database {...iconProps} />;
      case "Globe":
        return <Globe {...iconProps} />;
      case "Shield":
        return <Shield {...iconProps} />;
      case "Zap":
        return <Zap {...iconProps} />;
      case "Settings":
        return <Settings {...iconProps} />;
      case "Tag":
        return <Tag {...iconProps} />;
      case "Star":
        return <Star {...iconProps} />;
      case "CheckCircle":
        return <CheckCircle {...iconProps} />;
      case "AlertCircle":
        return <AlertCircle {...iconProps} />;
      case "Code":
        return <Code {...iconProps} />;
      case "Lock":
        return <Lock {...iconProps} />;
      case "Heart":
        return <Heart {...iconProps} />;
      case "Home":
        return <Home {...iconProps} />;
      case "User":
        return <User {...iconProps} />;
      case "Users":
        return <Users {...iconProps} />;
      case "Calendar":
        return <Calendar {...iconProps} />;
      case "FileText":
        return <FileText {...iconProps} />;
      case "Image":
        return <Image {...iconProps} />;
      case "Search":
        return <Search {...iconProps} />;
      case "Bell":
        return <Bell {...iconProps} />;
      case "MessageCircle":
        return <MessageCircle {...iconProps} />;
      case "Phone":
        return <Phone {...iconProps} />;
      case "MapPin":
        return <MapPin {...iconProps} />;
      case "Link":
        return <Link {...iconProps} />;
      case "TrendingUp":
        return <TrendingUp {...iconProps} />;
      case "BarChart":
        return <BarChart {...iconProps} />;
      case "Activity":
        return <Activity {...iconProps} />;
      case "Target":
        return <Target {...iconProps} />;
      case "Award":
        return <Award {...iconProps} />;
      case "ShoppingCart":
        return <ShoppingCart {...iconProps} />;
      case "CreditCard":
        return <CreditCard {...iconProps} />;
      case "DollarSign":
        return <DollarSign {...iconProps} />;
      case "Briefcase":
        return <Briefcase {...iconProps} />;
      case "Building":
        return <Building {...iconProps} />;
      case "Truck":
        return <Truck {...iconProps} />;
      case "Coffee":
        return <Coffee {...iconProps} />;
      case "Lightbulb":
        return <Lightbulb {...iconProps} />;
      case "Sun":
        return <Sun {...iconProps} />;
      case "Moon":
        return <Moon {...iconProps} />;
      case "Cloud":
        return <Cloud {...iconProps} />;
      case "Key":
        return <Key {...iconProps} />;
      case "Mail":
        return <Mail {...iconProps} />;
      default:
        return <Database {...iconProps} />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Metadata domeny
          </div>
          <Button
            onClick={startAdd}
            size="sm"
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Szybkie przyciski metadata */}
        {!isLoadingButtons && metadataButtons.length > 0 && (
          <div className="mb-6">
            <div className="flex items-center gap-2 mb-3">
              <Zap className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">
                Szybkie dodawanie
              </span>
            </div>
            <div className="flex flex-wrap gap-2">
              {metadataButtons.map((button) => (
                <Button
                  key={button.id}
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAddMetadata(button)}
                  disabled={isLoading || metadata.hasOwnProperty(button.key)}
                  className="flex items-center gap-2 hover:bg-accent hover:text-accent-foreground"
                  title={
                    button.description || `Dodaj ${button.key}: ${button.value}`
                  }
                >
                  {renderIcon(button.icon)}
                  <span>{button.label}</span>
                </Button>
              ))}
            </div>
          </div>
        )}

        {metadataEntries.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Brak metadata dla tej domeny</p>
            <p className="text-sm">
              {metadataButtons.length > 0
                ? 'Użyj szybkich przycisków powyżej lub kliknij "Dodaj metadata"'
                : 'Kliknij "Dodaj metadata" aby dodać informacje'}
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {metadataEntries.map(([key, value]) => (
              <div
                key={key}
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <Key className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium text-sm">{key}</span>
                    <Badge variant="outline" className="text-xs">
                      {getValueType(value)}
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground truncate">
                    {formatValue(value)}
                  </div>
                </div>
                <div className="flex items-center gap-2 ml-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => startEdit(key, value)}
                    className="h-8 w-8 p-0"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteMetadata(key)}
                    className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                    disabled={isLoading}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}

        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>
                {editingItem ? "Edytuj metadata" : "Dodaj metadata"}
              </DialogTitle>
              <DialogDescription>
                {editingItem
                  ? "Zaktualizuj klucz i wartość metadata"
                  : "Dodaj nowy klucz i wartość do metadata domeny"}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="key">Klucz</Label>
                <Input
                  id="key"
                  value={newKey}
                  onChange={(e) => setNewKey(e.target.value)}
                  placeholder="np. server_type, last_check, notes"
                  disabled={!!editingItem} // Nie pozwalaj zmieniać klucza podczas edycji
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="value">Wartość</Label>
                <Input
                  id="value"
                  value={newValue}
                  onChange={(e) => setNewValue(e.target.value)}
                  placeholder="Wartość (string, number, JSON object)"
                />
                <p className="text-xs text-muted-foreground">
                  Obsługiwane typy: string, number, boolean (true/false), JSON
                  object/array
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsDialogOpen(false)}
                disabled={isLoading}
              >
                <X className="h-4 w-4 mr-2" />
                Anuluj
              </Button>
              <Button onClick={handleSaveMetadata} disabled={isLoading}>
                <Save className="h-4 w-4 mr-2" />
                {isLoading ? "Zapisywanie..." : "Zapisz"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
