import { redirect } from "next/navigation";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

export default async function Home() {
  // Sprawdź czy użytkownik jest zalogowany
  const session = await getServerSession(authOptions);

  if (session) {
    // Jeśli użytkownik jest zalogowany, przekieruj na dashboard
    redirect("/dashboard");
  } else {
    // Jeśli użytkownik nie jest zalogowany, przekieruj na stronę logowania
    redirect("/login");
  }
}
