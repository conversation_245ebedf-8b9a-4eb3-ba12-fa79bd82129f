"use client";

import { useState, useEffect, use<PERSON>allback } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  Mail,
  Eye,
  MousePointer,
  Send,
  TrendingUp,
  Calendar,
  User,
  Link as LinkIcon,
  RefreshCw,
  Filter
} from "lucide-react";
import { format } from "date-fns";
import { pl } from "date-fns/locale";

interface TrackingEvent {
  id: string;
  emailHistoryId: string;
  domainId: string;
  recipientEmail: string;
  eventType: 'sent' | 'delivered' | 'opened' | 'clicked' | 'bounced' | 'complained';
  eventData?: {
    link?: string;
    userAgent?: string;
    ipAddress?: string;
    timestamp?: string;
    [key: string]: string | number | boolean | null | undefined;
  };
  createdAt: string;
  updatedAt: string;
  emailHistory?: {
    subject: string;
    sentAt: string;
    status: string;
  };
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

interface TrackingStats {
  summary: {
    totalEmails: number;
    totalTrackedEvents: number;
    openRate: number;
    clickRate: number;
    sentCount: number;
    openCount: number;
    clickCount: number;
  };
  eventStats: Record<string, number>;
  dailyStats: { date: string; count: number }[];
  linkStats: { date: string; count: number }[];
  domainStats: { date: string; count: number }[];
  period: {
    days: number;
    startDate: string;
    endDate: string;
  };
}

export default function EmailTrackingPage() {
  const [trackingEvents, setTrackingEvents] = useState<TrackingEvent[]>([]);
  const [stats, setStats] = useState<TrackingStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    eventType: 'all',
    recipientEmail: '',
    domainId: '',
    days: '30'
  });
  const [showFilters, setShowFilters] = useState(false);

  // Pobierz dane trackingu
  const fetchTrackingData = useCallback(async (page = 1) => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20',
        ...(filters.eventType && filters.eventType !== 'all' && { eventType: filters.eventType }),
        ...(filters.recipientEmail && { recipientEmail: filters.recipientEmail }),
        ...(filters.domainId && { domainId: filters.domainId }),
      });

      const response = await fetch(`/api/email-tracking?${params}`);
      const data = await response.json();

      if (data.success) {
        setTrackingEvents(data.data.trackingEvents);
        setCurrentPage(data.data.pagination.currentPage);
        setTotalPages(data.data.pagination.totalPages);
      } else {
        throw new Error(data.error || 'Błąd pobierania danych');
      }
    } catch (error) {
      console.error('Error fetching tracking data:', error);
      toast.error('Wystąpił błąd podczas pobierania danych trackingu');
    } finally {
      setIsLoading(false);
    }
  }, [filters]);

  // Pobierz statystyki
  const fetchStats = useCallback(async () => {
    try {
      const params = new URLSearchParams({
        days: filters.days,
        ...(filters.domainId && { domainId: filters.domainId }),
      });

      const response = await fetch(`/api/email-tracking/stats?${params}`);
      const data: ApiResponse<TrackingStats> = await response.json();

      if (data.success && data.data) {
        setStats(data.data);
      } else {
        throw new Error(data.error || 'Błąd pobierania statystyk');
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
      toast.error('Wystąpił błąd podczas pobierania statystyk');
    }
  }, [filters]);

  useEffect(() => {
    fetchTrackingData();
    fetchStats();
  }, [fetchTrackingData, fetchStats]);

  useEffect(() => {
    fetchTrackingData(1);
    fetchStats();
  }, [filters, fetchTrackingData, fetchStats]);

  const getEventIcon = (eventType: string) => {
    switch (eventType) {
      case 'sent':
        return <Send className="h-4 w-4" />;
      case 'opened':
        return <Eye className="h-4 w-4" />;
      case 'clicked':
        return <MousePointer className="h-4 w-4" />;
      default:
        return <Mail className="h-4 w-4" />;
    }
  };

  const getEventBadgeVariant = (eventType: string) => {
    switch (eventType) {
      case 'sent':
        return 'default';
      case 'opened':
        return 'secondary';
      case 'clicked':
        return 'destructive';
      case 'delivered':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd.MM.yyyy HH:mm', { locale: pl });
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-foreground">Śledzenie Maili</h1>
        <p className="text-muted-foreground">
          Monitoruj wysyłki, dostarczenia, otwarcia i kliknięcia w mailach audytowych.
        </p>
      </div>

      {/* Statystyki */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Wysłane maile</CardTitle>
              <Send className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.summary.sentCount}</div>
              <p className="text-xs text-muted-foreground">
                Ostatnie {stats.period.days} dni
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Wskaźnik otwarć</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.summary.openRate}%</div>
              <p className="text-xs text-muted-foreground">
                {stats.summary.openCount} z {stats.summary.sentCount} maili
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Wskaźnik kliknięć</CardTitle>
              <MousePointer className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.summary.clickRate}%</div>
              <p className="text-xs text-muted-foreground">
                {stats.summary.clickCount} z {stats.summary.sentCount} maili
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Wszystkie eventy</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.summary.totalTrackedEvents}</div>
              <p className="text-xs text-muted-foreground">
                Łączna liczba eventów
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filtry */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Eventy trackingu</CardTitle>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filtry
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchTrackingData(currentPage)}
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`} />
                Odśwież
              </Button>
            </div>
          </div>
        </CardHeader>

        {showFilters && (
          <CardContent className="border-t">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label>Typ eventu</Label>
                <Select
                  value={filters.eventType}
                  onValueChange={(value) => setFilters({ ...filters, eventType: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Wszystkie" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Wszystkie</SelectItem>
                    <SelectItem value="sent">Wysłane</SelectItem>
                    <SelectItem value="opened">Otwarte</SelectItem>
                    <SelectItem value="clicked">Kliknięte</SelectItem>
                    <SelectItem value="delivered">Dostarczone</SelectItem>
                    <SelectItem value="bounced">Odrzucone</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Email odbiorcy</Label>
                <Input
                  placeholder="np. <EMAIL>"
                  value={filters.recipientEmail}
                  onChange={(e) => setFilters({ ...filters, recipientEmail: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label>Okres</Label>
                <Select
                  value={filters.days}
                  onValueChange={(value) => setFilters({ ...filters, days: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7">Ostatnie 7 dni</SelectItem>
                    <SelectItem value="30">Ostatnie 30 dni</SelectItem>
                    <SelectItem value="90">Ostatnie 90 dni</SelectItem>
                    <SelectItem value="365">Ostatni rok</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>ID Domeny</Label>
                <Input
                  placeholder="ID domeny"
                  value={filters.domainId}
                  onChange={(e) => setFilters({ ...filters, domainId: e.target.value })}
                />
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Tabela eventów */}
      <Card>
        <CardContent className="p-0">
          {trackingEvents.length > 0 ? (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Data</TableHead>
                    <TableHead>Typ eventu</TableHead>
                    <TableHead>Odbiorca</TableHead>
                    <TableHead>Temat maila</TableHead>
                    <TableHead>Szczegóły</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {trackingEvents.map((event) => (
                    <TableRow key={event.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">
                            {formatDate(event.createdAt)}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getEventBadgeVariant(event.eventType)} className="flex items-center gap-1 w-fit">
                          {getEventIcon(event.eventType)}
                          {event.eventType}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{event.recipientEmail}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">
                          {event.emailHistory?.subject || 'Brak tematu'}
                        </span>
                      </TableCell>
                      <TableCell>
                        {event.eventType === 'clicked' && event.eventData?.link && (
                          <div className="flex items-center gap-2">
                            <LinkIcon className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm text-blue-600 truncate max-w-xs">
                              {event.eventData.link}
                            </span>
                          </div>
                        )}
                        {event.eventData?.userAgent && (
                          <div className="text-xs text-muted-foreground truncate max-w-xs">
                            {event.eventData.userAgent}
                          </div>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <Mail className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Brak danych trackingu</p>
              <p className="text-sm">
                Eventy trackingu pojawią się tutaj po wysłaniu maili z włączonym śledzeniem
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Paginacja */}
      {totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            onClick={() => fetchTrackingData(currentPage - 1)}
            disabled={currentPage <= 1 || isLoading}
          >
            Poprzednia
          </Button>
          <span className="flex items-center px-4">
            Strona {currentPage} z {totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => fetchTrackingData(currentPage + 1)}
            disabled={currentPage >= totalPages || isLoading}
          >
            Następna
          </Button>
        </div>
      )}
    </div>
  );
}
