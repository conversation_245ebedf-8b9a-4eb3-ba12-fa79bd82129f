// Skrypt do tworzenia indeksów MongoDB dla lepszej w<PERSON>
// Uruchom: node scripts/create-indexes.js

const { MongoClient } = require("mongodb");
const fs = require("fs");
const path = require("path");

// Funkcja do odczytania zmiennych środowiskowych z .env.local
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, "..", ".env.local");
    const envContent = fs.readFileSync(envPath, "utf8");
    const lines = envContent.split("\n");

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith("#")) {
        const [key, ...valueParts] = trimmedLine.split("=");
        if (key && valueParts.length > 0) {
          process.env[key.trim()] = valueParts.join("=").trim();
        }
      }
    }
  } catch (error) {
    console.error("Nie można odczytać pliku .env.local:", error.message);
  }
}

loadEnvFile();

async function createIndexes() {
  const client = new MongoClient(process.env.MONGODB_URI);

  try {
    await client.connect();
    console.log("Połączono z MongoDB");

    const db = client.db();
    const domainsCollection = db.collection("domains");

    // Indeks dla szybkiego wyszukiwania domen do przeglądu
    await domainsCollection.createIndex(
      { userId: 1, cms: 1, status: 1, createdAt: 1 },
      {
        name: "review_domains_index",
        background: true,
      }
    );
    console.log("✅ Utworzono indeks review_domains_index");

    // Indeks dla wyszukiwania domen użytkownika
    await domainsCollection.createIndex(
      { userId: 1, createdAt: -1 },
      {
        name: "user_domains_index",
        background: true,
      }
    );
    console.log("✅ Utworzono indeks user_domains_index");

    // Indeks dla wyszukiwania po statusie
    await domainsCollection.createIndex(
      { userId: 1, status: 1 },
      {
        name: "user_status_index",
        background: true,
      }
    );
    console.log("✅ Utworzono indeks user_status_index");

    // Indeks dla wyszukiwania po CMS
    await domainsCollection.createIndex(
      { userId: 1, cms: 1 },
      {
        name: "user_cms_index",
        background: true,
      }
    );
    console.log("✅ Utworzono indeks user_cms_index");

    // Indeks dla wyszukiwania po kategorii
    await domainsCollection.createIndex(
      { userId: 1, category: 1 },
      {
        name: "user_category_index",
        background: true,
      }
    );
    console.log("✅ Utworzono indeks user_category_index");

    // Indeks tekstowy dla wyszukiwania po nazwie domeny
    await domainsCollection.createIndex(
      {
        userId: 1,
        domain: "text",
        fullDomain: "text",
      },
      {
        name: "domain_search_index",
        background: true,
      }
    );
    console.log("✅ Utworzono indeks domain_search_index");

    console.log("\n🎉 Wszystkie indeksy zostały utworzone pomyślnie!");
  } catch (error) {
    console.error("❌ Błąd podczas tworzenia indeksów:", error);
  } finally {
    await client.close();
    console.log("Rozłączono z MongoDB");
  }
}

createIndexes();
