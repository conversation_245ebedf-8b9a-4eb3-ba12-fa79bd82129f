import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import EmailTracking from '@/models/EmailTracking';
import EmailHistory from '@/models/EmailHistory';

// GET endpoint to fetch email tracking data
export async function GET(request: NextRequest) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const domainId = searchParams.get('domainId');
    const emailHistoryId = searchParams.get('emailHistoryId');
    const eventType = searchParams.get('eventType');
    const recipientEmail = searchParams.get('recipientEmail');

    // Buduj query
    const query: {
      domainId?: string;
      emailHistoryId?: string;
      eventType?: string;
      recipientEmail?: string | { $regex: string; $options: string };
    } = {};

    if (domainId) {
      query.domainId = domainId;
    }

    if (emailHistoryId) {
      query.emailHistoryId = emailHistoryId;
    }

    if (eventType) {
      query.eventType = eventType;
    }

    if (recipientEmail) {
      query.recipientEmail = { $regex: recipientEmail, $options: 'i' };
    }

    // Pobierz tracking events z paginacją
    const skip = (page - 1) * limit;

    const [trackingEvents, totalCount] = await Promise.all([
      EmailTracking.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      EmailTracking.countDocuments(query)
    ]);

    // Pobierz powiązane EmailHistory dla dodatkowych informacji
    const emailHistoryIds = [...new Set(trackingEvents.map(event => event.emailHistoryId))];
    const emailHistories = await EmailHistory.find({
      _id: { $in: emailHistoryIds }
    }).lean();

    // Utwórz mapę EmailHistory dla szybkiego dostępu
    const emailHistoryMap = new Map();
    emailHistories.forEach((history) => {
      emailHistoryMap.set((history._id as { toString(): string }).toString(), history);
    });

    // Wzbogać dane trackingu o informacje z EmailHistory
    const enrichedTrackingEvents = trackingEvents.map(event => {
      const emailHistory = emailHistoryMap.get(event.emailHistoryId);
      return {
        id: event._id,
        emailHistoryId: event.emailHistoryId,
        domainId: event.domainId,
        recipientEmail: event.recipientEmail,
        eventType: event.eventType,
        eventData: event.eventData,
        createdAt: event.createdAt,
        updatedAt: event.updatedAt,
        emailHistory: emailHistory ? {
          subject: emailHistory.subject,
          sentAt: emailHistory.sentAt,
          status: emailHistory.status
        } : null
      };
    });

    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      success: true,
      data: {
        trackingEvents: enrichedTrackingEvents,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Error fetching email tracking data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
