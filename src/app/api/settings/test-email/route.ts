import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Settings from '@/models/Settings';

// GET - Pobierz testowy email użytkownika
export async function GET() {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    let settings = await Settings.findOne({ userId: session.user.id });

    if (!settings) {
      // Utwórz domyślne ustawienia jeśli nie istnieją
      settings = new Settings({
        userId: session.user.id,
        metadataButtons: [],
        testEmail: ''
      });
      await settings.save();
    }

    return NextResponse.json({
      success: true,
      testEmail: settings.testEmail || ''
    });

  } catch (error) {
    console.error('Error fetching test email:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Aktualizuj testowy email użytkownika
export async function PUT(request: NextRequest) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { testEmail } = body;

    // Walidacja formatu email (jeśli nie jest pusty)
    if (testEmail && testEmail.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(testEmail.trim())) {
        return NextResponse.json(
          { success: false, error: 'Nieprawidłowy format adresu email' },
          { status: 400 }
        );
      }
    }

    await connectDB();

    let settings = await Settings.findOne({ userId: session.user.id });

    if (!settings) {
      // Utwórz nowe ustawienia
      settings = new Settings({
        userId: session.user.id,
        metadataButtons: [],
        testEmail: testEmail?.trim() || ''
      });
    } else {
      // Aktualizuj istniejące ustawienia
      settings.testEmail = testEmail?.trim() || '';
    }

    await settings.save();

    return NextResponse.json({
      success: true,
      message: 'Testowy email został zaktualizowany',
      testEmail: settings.testEmail
    });

  } catch (error) {
    console.error('Error updating test email:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
