import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import Log from '@/models/Log';
import { getServerSession } from "next-auth/next";
import { Session } from 'next-auth'; // Added Session import
import { authOptions } from '@/lib/auth';

interface LogFilter {
  level?: string;
  source?: { $regex: string; $options: string };
  userId?: string;
}

// POST - Create new log entry (public endpoint for external systems)
export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const { level, message, source, userId, metadata } = body;

    // Validate required fields
    if (!level || !message) {
      return NextResponse.json(
        {
          success: false,
          error: 'Level and message are required'
        },
        { status: 400 }
      );
    }

    // Validate level
    if (!['info', 'warn', 'error', 'debug'].includes(level)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Level must be one of: info, warn, error, debug'
        },
        { status: 400 }
      );
    }

    // Create new log entry
    const log = new Log({
      level,
      message,
      source,
      userId,
      metadata
    });

    await log.save();

    return NextResponse.json({
      success: true,
      message: 'Log entry created successfully',
      logId: log._id
    });

  } catch (error) {
    console.error('Error creating log entry:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal Server Error'
      },
      { status: 500 }
    );
  }
}

// GET - Fetch logs (protected endpoint for dashboard)
export async function GET(request: NextRequest) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null; // Explicitly cast session

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const level = searchParams.get('level');
    const source = searchParams.get('source');
    const userId = searchParams.get('userId');

    // Build filter
    const filter: LogFilter = {};

    if (level) {
      filter.level = level;
    }

    if (source) {
      filter.source = { $regex: source, $options: 'i' };
    }

    if (userId) {
      filter.userId = userId;
    }

    // Calculate skip
    const skip = (page - 1) * limit;

    // Fetch logs with pagination
    const [logs, totalCount] = await Promise.all([
      Log.find(filter)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Log.countDocuments(filter)
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return NextResponse.json({
      success: true,
      logs,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext,
        hasPrev
      }
    });

  } catch (error) {
    console.error('Error fetching logs:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// DELETE - Clear all logs (protected endpoint for dashboard)
export async function DELETE(request: NextRequest) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    // Count logs before deletion
    const totalLogsCount = await Log.countDocuments();

    if (totalLogsCount === 0) {
      return NextResponse.json({
        success: true,
        message: 'Brak logów do usunięcia',
        deletedCount: 0
      });
    }

    // Delete all logs
    const result = await Log.deleteMany({});

    return NextResponse.json({
      success: true,
      message: `Usunięto wszystkie logi (${result.deletedCount} wpisów)`,
      deletedCount: result.deletedCount
    });

  } catch (error) {
    console.error('Error clearing logs:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
