// src/components/layout/DashboardLayout.tsx
"use client";

import { SessionProvider } from "next-auth/react";
import TopNavigation from "./TopNavigation";
import { Toaster } from "@/components/ui/sonner";
import { useEffect } from "react";
import { toast } from "sonner";
import { ILog } from "@/models/Log";

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  useEffect(() => {
    const eventSource = new EventSource("/api/logs/stream");

    eventSource.onmessage = (event) => {
      try {
        const log: ILog = JSON.parse(event.data);

        let description = log.source ? `Source: ${log.source}` : undefined;
        if (log.userId) {
            description = description ? `${description} | User: ${log.userId}` : `User: ${log.userId}`;
        }

        // Prepare the title/message part as a React element for HTML rendering
        const messageHtml = <div dangerouslySetInnerHTML={{ __html: log.message }} />;

        switch (log.level) {
          case 'info':
            toast.info(messageHtml, { description, duration: 5000 });
            break;
          case 'warn':
            toast.warning(messageHtml, { description, duration: 7000 });
            break;
          case 'error':
            toast.error(messageHtml, { description, duration: 10000 });
            break;
          case 'debug':
            toast(messageHtml, { description: `DEBUG: ${description || ''}`, duration: 5000 });
            break;
          default:
            toast(messageHtml, { description, duration: 5000 });
            break;
        }
      } catch (error) {
        console.error("Failed to parse log event data:", error);
      }
    };

    eventSource.onerror = (error) => {
      console.error("EventSource failed:", error);
      eventSource.close();
    };

    return () => {
      console.log("Closing EventSource connection");
      eventSource.close();
    };
  }, []);

  return (
    <SessionProvider>
      <div className="min-h-screen bg-background transition-colors duration-300">
        <TopNavigation />
        <main className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="animate-in fade-in-0 duration-500">{children}</div>
        </main>
        <Toaster />
      </div>
    </SessionProvider>
  );
}
