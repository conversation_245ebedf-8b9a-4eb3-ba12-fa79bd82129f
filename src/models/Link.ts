import mongoose, { Document, Schema } from 'mongoose';

export interface ILink extends Document {
  url: string;
  title?: string;
  snippet?: string;
  category: string;
  query: string;
  location?: string;
  userId: string;
  searchQueryId: string;
  createdAt: Date;
  updatedAt: Date;
}

const LinkSchema: Schema = new Schema({
  url: {
    type: String,
    required: [true, 'URL jest wymagany'],
    trim: true,
    maxlength: [2000, 'URL nie może być dłuższy niż 2000 znaków']
  },
  title: {
    type: String,
    trim: true,
    maxlength: [500, 'Tytuł nie może być dłuższy niż 500 znaków']
  },
  snippet: {
    type: String,
    trim: true,
    maxlength: [1000, 'Snippet nie może być dłuższy niż 1000 znaków']
  },
  category: {
    type: String,
    required: [true, 'Kategoria jest wymagana'],
    trim: true,
    maxlength: [100, 'Kategoria nie może być dłu<PERSON><PERSON><PERSON> niż 100 znaków']
  },
  query: {
    type: String,
    required: [true, 'Query jest wymagane'],
    trim: true,
    maxlength: [500, 'Query nie może być dłuższe niż 500 znaków']
  },
  location: {
    type: String,
    required: false,
    trim: true,
    maxlength: [200, 'Lokalizacja nie może być dłuższa niż 200 znaków']
  },
  userId: {
    type: String,
    required: [true, 'ID użytkownika jest wymagane']
  },
  searchQueryId: {
    type: Schema.Types.ObjectId,
    ref: 'SearchQuery',
    required: [true, 'ID zapytania wyszukiwania jest wymagane']
  }
}, {
  timestamps: true
});

// Indeks dla szybszego wyszukiwania linków użytkownika
LinkSchema.index({ userId: 1, category: 1 });

// Indeks dla wyszukiwania po URL (unikalne linki)
LinkSchema.index({ url: 1 });

// Indeks dla wyszukiwania po zapytaniu
LinkSchema.index({ searchQueryId: 1 });

// Indeks dla wyszukiwania po lokalizacji
LinkSchema.index({ location: 1 });

export default mongoose.models.Link || mongoose.model<ILink>('Link', LinkSchema);
