// Skrypt do testowania unikalności domen
// Uruchom: node scripts/test-uniqueness.js

const { MongoClient, ObjectId } = require("mongodb");
const fs = require("fs");
const path = require("path");

// Funkcja do odczytania zmiennych środowiskowych z .env
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, "..", ".env");
    const envContent = fs.readFileSync(envPath, "utf8");
    const lines = envContent.split("\n");

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith("#")) {
        const [key, ...valueParts] = trimmedLine.split("=");
        if (key && valueParts.length > 0) {
          process.env[key.trim()] = valueParts.join("=").trim();
        }
      }
    }
  } catch (error) {
    console.error("Nie można odczytać pliku .env:", error.message);
  }
}

loadEnvFile();

async function testUniqueness() {
  const client = new MongoClient(process.env.MONGODB_URI);

  try {
    await client.connect();
    console.log("Połączono z MongoDB");

    const db = client.db();
    const domainsCollection = db.collection("domains");

    const testUserId = "test-user-uniqueness";
    const testDomain = "https://test-unikalnosc-domen.pl";

    console.log("\n🧪 Test unikalności domen");
    console.log(`Testowa domena: ${testDomain}`);
    console.log(`Testowy użytkownik: ${testUserId}`);

    // Usuń istniejące testowe domeny
    await domainsCollection.deleteMany({ 
      userId: testUserId,
      fullDomain: testDomain 
    });
    console.log("🧹 Usunięto poprzednie testowe domeny");

    // Test 1: Dodaj domenę do pierwszej kategorii
    console.log("\n📝 Test 1: Dodawanie domeny do kategorii 'Kategoria1'");
    try {
      const result1 = await domainsCollection.insertOne({
        domain: "test-unikalnosc-domen.pl",
        protocol: "https",
        fullDomain: testDomain,
        category: "Kategoria1",
        userId: testUserId,
        linkCount: 1,
        status: "new",
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log("✅ Pomyślnie dodano domenę do Kategoria1");
      console.log(`   ID: ${result1.insertedId}`);
    } catch (error) {
      console.log("❌ Błąd dodawania domeny do Kategoria1:", error.message);
    }

    // Test 2: Próba dodania tej samej domeny do drugiej kategorii
    console.log("\n📝 Test 2: Próba dodania tej samej domeny do kategorii 'Kategoria2'");
    try {
      const result2 = await domainsCollection.insertOne({
        domain: "test-unikalnosc-domen.pl",
        protocol: "https",
        fullDomain: testDomain,
        category: "Kategoria2",
        userId: testUserId,
        linkCount: 1,
        status: "new",
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log("❌ BŁĄD: Udało się dodać duplikat domeny!");
      console.log(`   ID: ${result2.insertedId}`);
    } catch (error) {
      if (error.message.includes("duplicate key") || error.code === 11000) {
        console.log("✅ Poprawnie zablokowano duplikat domeny");
        console.log(`   Błąd: ${error.message}`);
      } else {
        console.log("❌ Nieoczekiwany błąd:", error.message);
      }
    }

    // Test 3: Sprawdź ile domen zostało dodanych
    console.log("\n📊 Test 3: Sprawdzanie liczby domen w bazie");
    const count = await domainsCollection.countDocuments({
      userId: testUserId,
      fullDomain: testDomain
    });
    console.log(`Liczba domen ${testDomain} dla użytkownika ${testUserId}: ${count}`);
    
    if (count === 1) {
      console.log("✅ Unikalność działa poprawnie - tylko jedna domena");
    } else {
      console.log("❌ Unikalność nie działa - znaleziono duplikaty");
    }

    // Test 4: Sprawdź szczegóły istniejącej domeny
    console.log("\n🔍 Test 4: Szczegóły istniejącej domeny");
    const existingDomain = await domainsCollection.findOne({
      userId: testUserId,
      fullDomain: testDomain
    });
    
    if (existingDomain) {
      console.log("Znaleziona domena:");
      console.log(`  - ID: ${existingDomain._id}`);
      console.log(`  - Kategoria: ${existingDomain.category}`);
      console.log(`  - Status: ${existingDomain.status}`);
      console.log(`  - Linki: ${existingDomain.linkCount}`);
    } else {
      console.log("Nie znaleziono domeny");
    }

    // Test 5: Test z innym użytkownikiem
    console.log("\n👤 Test 5: Test z innym użytkownikiem");
    const testUserId2 = "test-user-uniqueness-2";
    
    try {
      const result3 = await domainsCollection.insertOne({
        domain: "test-unikalnosc-domen.pl",
        protocol: "https",
        fullDomain: testDomain,
        category: "Kategoria1",
        userId: testUserId2,
        linkCount: 1,
        status: "new",
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log("✅ Pomyślnie dodano tę samą domenę dla innego użytkownika");
      console.log(`   ID: ${result3.insertedId}`);
    } catch (error) {
      console.log("❌ Błąd dodawania domeny dla innego użytkownika:", error.message);
    }

    // Sprawdź łączną liczbę testowych domen
    const totalCount = await domainsCollection.countDocuments({
      fullDomain: testDomain
    });
    console.log(`\nŁączna liczba domen ${testDomain}: ${totalCount}`);

    // Wyczyść testowe dane
    console.log("\n🧹 Czyszczenie testowych danych");
    const deleteResult = await domainsCollection.deleteMany({
      fullDomain: testDomain
    });
    console.log(`Usunięto ${deleteResult.deletedCount} testowych domen`);

    console.log("\n🎉 Test unikalności zakończony");

  } catch (error) {
    console.error("❌ Błąd podczas testowania unikalności:", error);
  } finally {
    await client.close();
    console.log("\nRozłączono z MongoDB");
  }
}

// Sprawdź czy skrypt jest uruchamiany bezpośrednio
if (require.main === module) {
  console.log("🧪 Testowanie unikalności domen...");
  
  testUniqueness().catch(error => {
    console.error("Błąd:", error);
    process.exit(1);
  });
}

module.exports = { testUniqueness };
