"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Mail,
  Eye,
  MousePointer,
  Send,
  AlertCircle,
  TrendingUp,
  Calendar,
  ExternalLink,
  Activity,
  Users,
} from "lucide-react";

interface MailTrackingStatsProps {
  domainId: string;
}

interface TrackingStats {
  summary: {
    totalEmails: number;
    totalTrackedEvents: number;
    openRate: number;
    clickRate: number;
    sentCount: number;
    openCount: number;
    clickCount: number;
  };
  eventStats: Record<string, number>;
  dailyStats: Array<{
    _id: string;
    events: Array<{
      eventType: string;
      count: number;
    }>;
  }>;
  linkStats: Array<{
    link: string;
    clickCount: number;
    uniqueRecipients: number;
  }>;
  period: {
    days: number;
    startDate: string;
    endDate: string;
  };
}

interface TrackingEvent {
  id: string;
  emailHistoryId: string;
  domainId: string;
  recipientEmail: string;
  eventType: 'sent' | 'delivered' | 'opened' | 'clicked' | 'bounced' | 'complained';
  eventData?: {
    link?: string;
    userAgent?: string;
    ipAddress?: string;
    timestamp?: string;
    [key: string]: string | number | boolean | Date | null | undefined;
  };
  createdAt: string;
  updatedAt: string;
  emailHistory?: {
    subject: string;
    sentAt: string;
    status: string;
  };
}

export default function MailTrackingStats({ domainId }: MailTrackingStatsProps) {
  const [stats, setStats] = useState<TrackingStats | null>(null);
  const [events, setEvents] = useState<TrackingEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showEventsModal, setShowEventsModal] = useState(false);
  const [selectedEventType, setSelectedEventType] = useState<string>("");

  const fetchStats = useCallback(async () => {
    try {
      const response = await fetch(`/api/email-tracking/stats?domainId=${domainId}&days=30`);
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setStats(data.data);
        }
      }
    } catch (error) {
      console.error("Błąd pobierania statystyk mail trackingu:", error);
    }
  }, [domainId]);

  const fetchEvents = useCallback(async (eventType?: string) => {
    try {
      let url = `/api/email-tracking?domainId=${domainId}&limit=50`;
      if (eventType) {
        url += `&eventType=${eventType}`;
      }

      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setEvents(data.data.trackingEvents);
        }
      }
    } catch (error) {
      console.error("Błąd pobierania eventów mail trackingu:", error);
    }
  }, [domainId]);

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      await Promise.all([fetchStats(), fetchEvents()]);
      setIsLoading(false);
    };

    loadData();
  }, [domainId, fetchEvents, fetchStats]);

  const handleShowEvents = async (eventType: string) => {
    setSelectedEventType(eventType);
    await fetchEvents(eventType);
    setShowEventsModal(true);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pl-PL", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getEventTypeIcon = (eventType: string) => {
    switch (eventType) {
      case 'sent':
        return <Send className="h-4 w-4" />;
      case 'opened':
        return <Eye className="h-4 w-4" />;
      case 'clicked':
        return <MousePointer className="h-4 w-4" />;
      case 'bounced':
        return <AlertCircle className="h-4 w-4" />;
      case 'complained':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Mail className="h-4 w-4" />;
    }
  };

  const getEventTypeColor = (eventType: string) => {
    switch (eventType) {
      case 'sent':
        return 'bg-blue-500';
      case 'opened':
        return 'bg-green-500';
      case 'clicked':
        return 'bg-purple-500';
      case 'bounced':
        return 'bg-red-500';
      case 'complained':
        return 'bg-orange-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getEventTypeName = (eventType: string) => {
    switch (eventType) {
      case 'sent':
        return 'Wysłane';
      case 'opened':
        return 'Otwarte';
      case 'clicked':
        return 'Kliknięte';
      case 'bounced':
        return 'Odrzucone';
      case 'complained':
        return 'Spam';
      default:
        return eventType;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Mail Tracking
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="text-muted-foreground mt-2">Ładowanie danych...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!stats || stats.summary.totalEmails === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Mail Tracking
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <Mail className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Brak danych mail trackingu dla tej domeny</p>
            <p className="text-sm">Wyślij pierwszy email z audytem, aby zobaczyć statystyki</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Mail Tracking
            </div>
            <Badge variant="outline" className="text-xs">
              Ostatnie {stats.period.days} dni
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Główne statystyki */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {stats.summary.totalEmails}
                </div>
                <div className="text-sm text-muted-foreground">Wysłane emaile</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {stats.summary.openRate}%
                </div>
                <div className="text-sm text-muted-foreground">Wskaźnik otwarć</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {stats.summary.clickRate}%
                </div>
                <div className="text-sm text-muted-foreground">Wskaźnik kliknięć</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-600">
                  {stats.summary.totalTrackedEvents}
                </div>
                <div className="text-sm text-muted-foreground">Wszystkie eventy</div>
              </div>
            </div>

            {/* Szczegółowe statystyki eventów */}
            <div>
              <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Szczegóły eventów
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {Object.entries(stats.eventStats).map(([eventType, count]) => (
                  <Button
                    key={eventType}
                    variant="outline"
                    className="h-auto p-3 flex flex-col items-center gap-2"
                    onClick={() => handleShowEvents(eventType)}
                  >
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${getEventTypeColor(eventType)}`}></div>
                      {getEventTypeIcon(eventType)}
                    </div>
                    <div className="text-center">
                      <div className="font-semibold">{count}</div>
                      <div className="text-xs text-muted-foreground">
                        {getEventTypeName(eventType)}
                      </div>
                    </div>
                  </Button>
                ))}
              </div>
            </div>

            {/* Najczęściej klikane linki */}
            {stats.linkStats.length > 0 && (
              <div>
                <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
                  <MousePointer className="h-4 w-4" />
                  Najczęściej klikane linki
                </h4>
                <div className="space-y-2">
                  {stats.linkStats.slice(0, 5).map((linkStat, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 bg-muted rounded-lg"
                    >
                      <div className="flex items-center gap-2 flex-1 min-w-0">
                        <ExternalLink className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                        <span className="text-sm truncate" title={linkStat.link}>
                          {linkStat.link}
                        </span>
                      </div>
                      <div className="flex items-center gap-3 text-sm text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <MousePointer className="h-3 w-3" />
                          {linkStat.clickCount}
                        </span>
                        <span className="flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          {linkStat.uniqueRecipients}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Przycisk do wyświetlenia wszystkich eventów */}
            <div className="pt-4 border-t">
              <Button
                variant="outline"
                onClick={() => handleShowEvents("")}
                className="w-full"
              >
                <Calendar className="h-4 w-4 mr-2" />
                Zobacz wszystkie eventy
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Modal z eventami */}
      <Dialog open={showEventsModal} onOpenChange={setShowEventsModal}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Eventy Mail Tracking
              {selectedEventType && (
                <Badge variant="outline">
                  {getEventTypeName(selectedEventType)}
                </Badge>
              )}
            </DialogTitle>
          </DialogHeader>
          <ScrollArea className="max-h-[60vh] w-full">
            <div className="space-y-3">
              {events.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Brak eventów do wyświetlenia</p>
                </div>
              ) : (
                events.map((event) => (
                  <div
                    key={event.id}
                    className="border rounded-lg p-4 space-y-2"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${getEventTypeColor(event.eventType)}`}></div>
                        {getEventTypeIcon(event.eventType)}
                        <span className="font-medium">
                          {getEventTypeName(event.eventType)}
                        </span>
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {formatDate(event.createdAt)}
                      </span>
                    </div>

                    <div className="text-sm space-y-1">
                      <div>
                        <span className="text-muted-foreground">Email:</span>{" "}
                        {event.recipientEmail}
                      </div>

                      {event.emailHistory && (
                        <div>
                          <span className="text-muted-foreground">Temat:</span>{" "}
                          {event.emailHistory.subject}
                        </div>
                      )}

                      {event.eventData?.link && (
                        <div>
                          <span className="text-muted-foreground">Link:</span>{" "}
                          <a
                            href={event.eventData.link}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline"
                          >
                            {event.eventData.link}
                          </a>
                        </div>
                      )}

                      {event.eventData?.userAgent && (
                        <div>
                          <span className="text-muted-foreground">User Agent:</span>{" "}
                          <span className="text-xs">{event.eventData.userAgent}</span>
                        </div>
                      )}

                      {event.eventData?.ipAddress && (
                        <div>
                          <span className="text-muted-foreground">IP:</span>{" "}
                          {event.eventData.ipAddress}
                        </div>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </>
  );
}
