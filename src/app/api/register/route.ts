import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import connectDB from '@/lib/db';
import User from '@/models/User';
import Category from '@/models/Category';

export async function POST(request: NextRequest) {
  try {
    const { email, password, name } = await request.json();

    if (!email || !password || !name) {
      return NextResponse.json(
        { error: 'Wszystkie pola są wymagane' },
        { status: 400 }
      );
    }

    if (password.length < 6) {
      return NextResponse.json(
        { error: 'Hasło musi mieć co najmniej 6 znaków' },
        { status: 400 }
      );
    }

    // Połączenie z bazą danych
    await connectDB();

    // Sprawdzenie czy użytkownik już istnieje
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return NextResponse.json(
        { error: 'Użytkownik z tym adresem email już istnieje' },
        { status: 400 }
      );
    }

    // Hashowanie hasła
    const hashedPassword = await bcrypt.hash(password, 12);

    // Utworzenie nowego użytkownika
    const user = await User.create({
      email,
      password: hashedPassword,
      name,
    });

    // Utworzenie domyślnych kategorii dla nowego użytkownika
    const defaultCategories = [
      {
        name: 'Programowanie',
        description: 'Artykuły i zasoby związane z programowaniem',
        userId: user._id.toString(),
        isDefault: true,
      },
      {
        name: 'Technologia',
        description: 'Najnowsze trendy i nowości technologiczne',
        userId: user._id.toString(),
        isDefault: true,
      },
      {
        name: 'Nauka',
        description: 'Materiały edukacyjne i naukowe',
        userId: user._id.toString(),
        isDefault: true,
      },
    ];

    try {
      await Category.insertMany(defaultCategories);
    } catch (error) {
      console.error('Błąd tworzenia domyślnych kategorii:', error);
      // Nie przerywamy procesu rejestracji jeśli nie udało się utworzyć kategorii
    }

    // Zwrócenie odpowiedzi bez hasła
    return NextResponse.json(
      {
        message: 'Użytkownik został utworzony pomyślnie',
        user: {
          id: user._id,
          email: user.email,
          name: user.name,
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Błąd rejestracji:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas rejestracji' },
      { status: 500 }
    );
  }
}
