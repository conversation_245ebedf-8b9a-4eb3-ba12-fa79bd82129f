// Skrypt migracji do zmiany unikalności domen z per-kategoria na globalną
// Uruchom: node scripts/migrate-domain-uniqueness.js

const { MongoClient } = require("mongodb");
const fs = require("fs");
const path = require("path");

// Funkcja do odczytania zmiennych środowiskowych z .env
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, "..", ".env");
    const envContent = fs.readFileSync(envPath, "utf8");
    const lines = envContent.split("\n");

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith("#")) {
        const [key, ...valueParts] = trimmedLine.split("=");
        if (key && valueParts.length > 0) {
          process.env[key.trim()] = valueParts.join("=").trim();
        }
      }
    }
  } catch (error) {
    console.error("Nie można odczytać pliku .env:", error.message);
  }
}

loadEnvFile();

async function migrateDomainUniqueness() {
  const client = new MongoClient(process.env.MONGODB_URI);

  try {
    await client.connect();
    console.log("Połączono z MongoDB");

    const db = client.db();
    const domainsCollection = db.collection("domains");

    console.log("\n🔍 Sprawdzanie obecnych indeksów...");
    const indexes = await domainsCollection.indexes();
    console.log("Obecne indeksy:", indexes.map(idx => ({ name: idx.name, key: idx.key })));

    console.log("\n🔍 Sprawdzanie duplikatów domen...");

    // Znajdź duplikaty domen (ta sama fullDomain dla tego samego userId)
    const duplicates = await domainsCollection.aggregate([
      {
        $group: {
          _id: { fullDomain: "$fullDomain", userId: "$userId" },
          domains: {
            $push: {
              id: "$_id",
              category: "$category",
              status: "$status",
              linkCount: "$linkCount",
              createdAt: "$createdAt"
            }
          },
          count: { $sum: 1 }
        }
      },
      {
        $match: { count: { $gt: 1 } }
      },
      {
        $sort: { count: -1 }
      }
    ]).toArray();

    console.log(`Znaleziono ${duplicates.length} grup duplikatów`);

    if (duplicates.length > 0) {
      console.log("\n📋 Przykłady duplikatów:");
      duplicates.slice(0, 5).forEach((dup, index) => {
        console.log(`${index + 1}. ${dup._id.fullDomain} (${dup.count} duplikatów)`);
        dup.domains.forEach((domain, i) => {
          console.log(`   ${i + 1}. Kategoria: ${domain.category}, Status: ${domain.status}, Linki: ${domain.linkCount}`);
        });
      });

      console.log("\n⚠️  UWAGA: Znaleziono duplikaty domen!");
      console.log("Przed zmianą indeksu unikalności należy usunąć duplikaty.");
      console.log("Użyj API endpoint: GET /api/domains/check-duplicates");
      console.log("Następnie: POST /api/domains/check-duplicates z odpowiednią strategią");

      // Nie kontynuuj migracji jeśli są duplikaty
      return;
    }

    console.log("\n✅ Brak duplikatów - można kontynuować migrację");

    console.log("\n🗑️  Sprawdzanie i usuwanie starego indeksu unikalności...");
    try {
      // Usuń stary indeks unikalności (userId + fullDomain + category)
      await domainsCollection.dropIndex({ userId: 1, fullDomain: 1, category: 1 });
      console.log("✅ Usunięto stary indeks unikalności");
    } catch (error) {
      if (error.message.includes("index not found") || error.message.includes("can't find index")) {
        console.log("ℹ️  Stary indeks nie istnieje - kontynuowanie");
      } else {
        throw error;
      }
    }

    console.log("\n🔧 Sprawdzanie i tworzenie nowego indeksu unikalności...");
    // Sprawdź czy nowy indeks już istnieje
    const currentIndexesForUnique = await domainsCollection.indexes();
    const hasUniqueIndex = currentIndexesForUnique.some(idx =>
      idx.name === "user_domain_unique_index" ||
      (idx.key && idx.key.userId === 1 && idx.key.fullDomain === 1 && idx.unique)
    );

    if (!hasUniqueIndex) {
      // Utwórz nowy indeks unikalności (userId + fullDomain)
      await domainsCollection.createIndex(
        { userId: 1, fullDomain: 1 },
        {
          unique: true,
          name: "user_domain_unique_index",
          background: true,
        }
      );
      console.log("✅ Utworzono nowy indeks unikalności (userId + fullDomain)");
    } else {
      console.log("ℹ️  Nowy indeks unikalności już istnieje");
    }

    console.log("\n🔧 Sprawdzanie i tworzenie dodatkowych indeksów...");

    // Pobierz aktualne indeksy
    const currentIndexes = await domainsCollection.indexes();
    const indexNames = currentIndexes.map(idx => idx.name);

    // Indeks dla wyszukiwania po kategorii (bez unikalności)
    if (!indexNames.includes("user_category_index") && !indexNames.includes("userId_1_category_1")) {
      await domainsCollection.createIndex(
        { userId: 1, category: 1 },
        {
          name: "user_category_index",
          background: true,
        }
      );
      console.log("✅ Utworzono indeks user_category_index");
    } else {
      console.log("ℹ️  Indeks user_category_index już istnieje");
    }

    // Indeks dla wyszukiwania po statusie
    if (!indexNames.includes("user_status_index")) {
      await domainsCollection.createIndex(
        { userId: 1, status: 1 },
        {
          name: "user_status_index",
          background: true,
        }
      );
      console.log("✅ Utworzono indeks user_status_index");
    } else {
      console.log("ℹ️  Indeks user_status_index już istnieje");
    }

    // Indeks dla wyszukiwania po CMS
    if (!indexNames.includes("user_cms_index")) {
      await domainsCollection.createIndex(
        { userId: 1, cms: 1 },
        {
          name: "user_cms_index",
          background: true,
        }
      );
      console.log("✅ Utworzono indeks user_cms_index");
    } else {
      console.log("ℹ️  Indeks user_cms_index już istnieje");
    }

    console.log("\n🔍 Sprawdzanie nowych indeksów...");
    const newIndexes = await domainsCollection.indexes();
    console.log("Nowe indeksy:");
    newIndexes.forEach(idx => {
      console.log(`  - ${idx.name}: ${JSON.stringify(idx.key)} ${idx.unique ? '(UNIQUE)' : ''}`);
    });

    console.log("\n✅ Migracja zakończona pomyślnie!");
    console.log("Domeny są teraz unikalne globalnie dla każdego użytkownika (nie per-kategoria)");

  } catch (error) {
    console.error("❌ Błąd podczas migracji:", error);
    process.exit(1);
  } finally {
    await client.close();
    console.log("Rozłączono z MongoDB");
  }
}

// Sprawdź czy skrypt jest uruchamiany bezpośrednio
if (require.main === module) {
  console.log("🚀 Rozpoczynanie migracji unikalności domen...");
  console.log("Ta migracja zmieni unikalność domen z per-kategoria na globalną dla użytkownika");

  migrateDomainUniqueness().catch(error => {
    console.error("Błąd migracji:", error);
    process.exit(1);
  });
}

module.exports = { migrateDomainUniqueness };
