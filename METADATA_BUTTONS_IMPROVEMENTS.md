# Ulepszenia przycisków metadata - Wyświetlanie ikon

## Wprowadzone zmiany

### 1. Naprawiono błąd aktualizacji przycisków metadata
- **Problem**: Błąd "Wystą<PERSON>ł błąd podczas aktualizacji przycisku metadata" spowodowany utratą pola `id` podczas aktualizacji
- **Rozwiązanie**: Zmieniono logikę aktualizacji z zastępowania całego obiektu na bezpośrednią modyfikację wła<PERSON>ści
- **Pliki**: `src/app/api/settings/metadata-buttons/[id]/route.ts`

### 2. Ulepszono wyświetlanie ikon w selektorach
- **Dodano**: Podgląd ikon w selektorach podczas dodawania/edycji przycisków
- **Funkcjonalność**: Użytkownik widzi ikonę obok nazwy w liście wyboru
- **Pliki**: `src/app/dashboard/ustawienia/page.tsx`

### 3. Rozszerzono dostępne ikony
- **Dodano**: 30+ nowych ikon z biblioteki Lucide React
- **Kategorie**: 
  - Podstawowe: Code, Lock, Heart, Home, User, Users
  - Komunikacja: Bell, MessageCircle, Phone, Mail
  - Biznes: Briefcase, Building, CreditCard, DollarSign
  - Inne: Calendar, Search, Image, Coffee, Lightbulb, Sun, Moon, Cloud
- **Pliki**: `src/app/dashboard/ustawienia/page.tsx`, `src/components/domain/MetadataManager.tsx`

### 4. Poprawiono wyświetlanie ikon w liście przycisków
- **Zmiana**: Zastąpiono statyczną ikonę Database odpowiednią ikoną przycisku
- **Efekt**: Każdy przycisk w ustawieniach wyświetla swoją przypisaną ikonę
- **Pliki**: `src/app/dashboard/ustawienia/page.tsx`

### 5. Dodano podgląd wybranej ikony w triggerze selektora
- **Funkcjonalność**: Wybrany element w selektorze pokazuje ikonę i nazwę
- **Dotyczy**: Formularze dodawania i edycji przycisków metadata
- **Pliki**: `src/app/dashboard/ustawienia/page.tsx`

## Funkcjonalność już istniejąca

### Wyświetlanie ikon w przyciskach metadata
- **Lokalizacja**: Sekcja "Szybkie dodawanie" w komponencie MetadataManager
- **Funkcjonalność**: Każdy przycisk wyświetla odpowiednią ikonę przypisaną do wartości metadata
- **Implementacja**: Funkcja `renderIcon()` w `src/components/domain/MetadataManager.tsx`

## Dostępne ikony

### Podstawowe
- Server, Database, Globe, Shield, Zap, Settings, Tag, Star
- CheckCircle, AlertCircle, Code, Lock, Heart, Home

### Użytkownicy i komunikacja
- User, Users, Bell, MessageCircle, Phone, Mail

### Biznes i finanse
- Briefcase, Building, CreditCard, DollarSign, ShoppingCart, Award, Target

### Narzędzia i akcje
- Search, Calendar, FileText, Image, Link, TrendingUp, BarChart, Activity

### Transport i inne
- Truck, Coffee, Lightbulb, Sun, Moon, Cloud, Key

## Jak używać

### 1. Dodawanie nowego przycisku metadata
1. Przejdź do Ustawienia → Przyciski metadata
2. Kliknij "Dodaj przycisk"
3. Wypełnij formularz:
   - **Etykieta**: Nazwa wyświetlana na przycisku
   - **Klucz**: Klucz metadata (np. "ssl_enabled")
   - **Wartość**: Wartość metadata (np. "true")
   - **Ikona**: Wybierz z listy dostępnych ikon (z podglądem)
   - **Opis**: Opcjonalny opis przycisku
4. Zapisz przycisk

### 2. Używanie przycisków metadata
1. Przejdź do szczegółów domeny
2. W sekcji "Metadata domeny" znajdź "Szybkie dodawanie"
3. Kliknij przycisk z odpowiednią ikoną
4. Metadata zostanie automatycznie dodana do domeny

### 3. Edycja istniejących przycisków
1. W ustawieniach przycisków metadata kliknij ikonę edycji
2. Zmień dowolne pola (w tym ikonę)
3. Zapisz zmiany

## Pliki zmodyfikowane

1. `src/app/api/settings/metadata-buttons/[id]/route.ts` - Naprawiono błąd aktualizacji
2. `src/app/dashboard/ustawienia/page.tsx` - Ulepszono interfejs selektorów ikon
3. `src/components/domain/MetadataManager.tsx` - Rozszerzono obsługę ikon

## Testowanie

Aby przetestować funkcjonalność:
1. Utwórz nowy przycisk metadata z różnymi ikonami
2. Sprawdź, czy ikony są wyświetlane w selektorach
3. Sprawdź, czy ikony są wyświetlane w liście przycisków
4. Sprawdź, czy ikony są wyświetlane w sekcji "Szybkie dodawanie"
5. Przetestuj edycję istniejących przycisków
