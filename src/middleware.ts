import { withAuth } from 'next-auth/middleware';

export default withAuth(
  function middleware(req) {
    console.log(req)
    // Middleware logic here if needed
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Sprawdź czy użytkownik próbuje uzyskać dostęp do chronionej trasy
        if (req.nextUrl.pathname.startsWith('/dashboard')) {
          return !!token; // Zwróć true jeśli token istnieje
        }
        return true; // Pozwól na dostęp do innych tras
      },
    },
  }
);

export const config = {
  matcher: ['/dashboard/:path*']
};
