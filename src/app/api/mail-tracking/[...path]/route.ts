import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import EmailTracking from '@/models/EmailTracking';
import EmailHistory from '@/models/EmailHistory';
import connectDB from '@/lib/db';

interface JWTPayload {
  recipient: string;
  link?: string;
  [key: string]: unknown;
}

// Prosty SVG pixel do trackingu
const trackingPixel = `<svg width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" fill="transparent"/></svg>`;

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const { path } = await params;
    const jwtSecret = process.env.NEXTAUTH_SECRET || 'fallback-secret';

    if (path[0] === 'blank-image' && path[1]) {
      // Obsługa trackingu otwarcia maila
      try {
        const decoded = jwt.verify(path[1], jwtSecret) as JWTPayload;

        await connectDB();

        // Znajdź EmailHistory na podstawie recipient
        const emailHistory = await EmailHistory.findOne({
          recipientEmail: decoded.recipient
        }).sort({ createdAt: -1 });

        if (emailHistory) {
          // Sprawdź czy email już został otwarty
          const existingOpenEvent = await EmailTracking.findOne({
            emailHistoryId: emailHistory._id,
            recipientEmail: decoded.recipient,
            eventType: 'opened'
          });

          if (existingOpenEvent) {
            console.log(`Email already opened by ${decoded.recipient}, skipping duplicate event`);
          } else {
            // Zapisz event otwarcia tylko jeśli nie istnieje
            const trackingEvent = new EmailTracking({
              emailHistoryId: emailHistory._id,
              domainId: emailHistory.domainId,
              recipientEmail: decoded.recipient,
              eventType: 'opened',
              eventData: {
                timestamp: new Date(),
                userAgent: request.headers.get('user-agent') || undefined,
                ipAddress: request.headers.get('x-forwarded-for') ||
                           request.headers.get('x-real-ip') ||
                           'unknown',
                source: 'ai-studio-audit'
              }
            });

            await trackingEvent.save();
            console.log(`Email opened event saved for ${decoded.recipient}`);
          }
        }

        // Zwróć transparentny pixel
        return new NextResponse(trackingPixel, {
          headers: {
            'Content-Type': 'image/svg+xml',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        });

      } catch (jwtError) {
        console.error('Invalid JWT token for tracking pixel:', jwtError);
        return new NextResponse(trackingPixel, {
          headers: { 'Content-Type': 'image/svg+xml' }
        });
      }
    }

    if (path[0] === 'link' && path[1]) {
      // Obsługa trackingu kliknięć w linki
      try {
        const decoded = jwt.verify(path[1], jwtSecret) as JWTPayload;

        await connectDB();

        // Znajdź EmailHistory na podstawie recipient
        const emailHistory = await EmailHistory.findOne({
          recipientEmail: decoded.recipient
        }).sort({ createdAt: -1 });

        if (emailHistory) {
          // Zapisz event kliknięcia
          const trackingEvent = new EmailTracking({
            emailHistoryId: emailHistory._id,
            domainId: emailHistory.domainId,
            recipientEmail: decoded.recipient,
            eventType: 'clicked',
            eventData: {
              link: decoded.link,
              timestamp: new Date(),
              userAgent: request.headers.get('user-agent') || undefined,
              ipAddress: request.headers.get('x-forwarded-for') ||
                         request.headers.get('x-real-ip') ||
                         'unknown',
              source: 'ai-studio-audit'
            }
          });

          await trackingEvent.save();
          console.log(`Link clicked event saved for ${decoded.recipient}: ${decoded.link}`);
        }

        // Przekieruj do oryginalnego linku
        if (decoded.link) {
          return NextResponse.redirect(decoded.link);
        } else {
          return NextResponse.json({ error: 'No link found in tracking data' }, { status: 400 });
        }

      } catch (jwtError) {
        console.error('Invalid JWT token for link tracking:', jwtError);
        return NextResponse.json({ error: 'Invalid tracking link' }, { status: 400 });
      }
    }

    return NextResponse.json({ error: 'Not found' }, { status: 404 });

  } catch (error) {
    console.error('Error in mail tracking route:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
