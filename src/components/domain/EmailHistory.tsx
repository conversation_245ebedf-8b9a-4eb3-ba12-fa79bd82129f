"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle} from "@/components/ui/dialog";
import { <PERSON>rollArea } from "@/components/ui/scroll-area";
import { toast } from "sonner";
import {
  Mail,
  Calendar,
  User,
  FileText,
  CheckCircle,
  XCircle,
  Clock,
  Trash2,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  Eye,
  ExternalLink,
} from "lucide-react";

interface EmailHistoryEntry {
  id: string;
  recipientEmail: string;
  subject: string;
  emailType: "audit" | "notification" | "other";
  status: "sent" | "failed" | "pending";
  sentAt: string;
  metadata?: {
    messageId?: string;
    errorMessage?: string;
    auditContentLength?: number;
    [key: string]: string | number | boolean | null | undefined;
  };
  hasContent: boolean;
  createdAt: string;
  updatedAt: string;
}

interface EmailContentData {
  id: string;
  recipientEmail: string;
  subject: string;
  emailType: "audit" | "notification" | "other";
  status: "sent" | "failed" | "pending";
  sentAt: string;
  emailContent: string;
  metadata?: {
    messageId?: string;
    errorMessage?: string;
    auditContentLength?: number;
    [key: string]: string | number | boolean | null | undefined;
  };
  createdAt: string;
  updatedAt: string;
}

interface EmailHistoryData {
  emailHistory: EmailHistoryEntry[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalCount: number;
    limit: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  domain: {
    id: string;
    domain: string;
    fullDomain: string;
  };
}

interface EmailHistoryProps {
  domainId: string;
}

export default function EmailHistory({ domainId }: EmailHistoryProps) {
  const [data, setData] = useState<EmailHistoryData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [emailTypeFilter, setEmailTypeFilter] = useState<string>("all");
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  // Modal state for email content preview
  const [showContentModal, setShowContentModal] = useState(false);
  const [selectedEmailContent, setSelectedEmailContent] = useState<EmailContentData | null>(null);
  const [isLoadingContent, setIsLoadingContent] = useState(false);

  const fetchEmailHistory = useCallback(async (page: number = 1) => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "10",
      });

      if (statusFilter !== "all") {
        params.append("status", statusFilter);
      }

      if (emailTypeFilter !== "all") {
        params.append("emailType", emailTypeFilter);
      }

      const response = await fetch(
        `/api/domains/${domainId}/email-history?${params}`
      );

      if (!response.ok) {
        throw new Error("Błąd pobierania historii maili");
      }

      const result = await response.json();

      if (result.success) {
        setData(result.data);
        setCurrentPage(page);
      } else {
        throw new Error(result.error || "Błąd pobierania historii maili");
      }
    } catch (error) {
      console.error("Błąd pobierania historii maili:", error);
      toast.error("Wystąpił błąd podczas pobierania historii maili");
    } finally {
      setIsLoading(false);
    }
  }, [domainId, statusFilter, emailTypeFilter]);

  useEffect(() => {
    fetchEmailHistory(1);
  }, [fetchEmailHistory]);

  const handleDeleteEntry = async (historyId: string) => {
    setIsDeleting(historyId);
    try {
      const response = await fetch(
        `/api/domains/${domainId}/email-history?historyId=${historyId}`,
        {
          method: "DELETE",
        }
      );

      const result = await response.json();

      if (result.success) {
        toast.success("Wpis historii został usunięty");
        fetchEmailHistory(currentPage);
      } else {
        throw new Error(result.error || "Błąd usuwania wpisu");
      }
    } catch (error) {
      console.error("Błąd usuwania wpisu:", error);
      toast.error("Wystąpił błąd podczas usuwania wpisu");
    } finally {
      setIsDeleting(null);
    }
  };

  const handleViewEmailContent = async (historyId: string) => {
    setIsLoadingContent(true);
    try {
      const response = await fetch(
        `/api/domains/${domainId}/email-history/${historyId}`
      );

      if (!response.ok) {
        throw new Error("Błąd pobierania treści maila");
      }

      const result = await response.json();

      if (result.success) {
        setSelectedEmailContent(result.data);
        setShowContentModal(true);
      } else {
        throw new Error(result.error || "Błąd pobierania treści maila");
      }
    } catch (error) {
      console.error("Błąd pobierania treści maila:", error);
      toast.error("Wystąpił błąd podczas pobierania treści maila");
    } finally {
      setIsLoadingContent(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "sent":
        return (
          <Badge variant="default" className="bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Wysłany
          </Badge>
        );
      case "failed":
        return (
          <Badge variant="destructive">
            <XCircle className="h-3 w-3 mr-1" />
            Błąd
          </Badge>
        );
      case "pending":
        return (
          <Badge variant="secondary">
            <Clock className="h-3 w-3 mr-1" />
            Oczekuje
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getEmailTypeBadge = (emailType: string) => {
    switch (emailType) {
      case "audit":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700">
            <FileText className="h-3 w-3 mr-1" />
            Audyt
          </Badge>
        );
      case "notification":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700">
            <Mail className="h-3 w-3 mr-1" />
            Powiadomienie
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            <Mail className="h-3 w-3 mr-1" />
            Inne
          </Badge>
        );
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("pl-PL", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (isLoading && !data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Historia wysłanych maili
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            <span>Ładowanie historii maili...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Historia wysłanych maili
          </div>
          <Button
            onClick={() => fetchEmailHistory(currentPage)}
            variant="outline"
            size="sm"
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`} />
            Odśwież
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Filtry */}
        <div className="flex gap-4 mb-6">
          <div className="flex-1">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filtruj po statusie" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Wszystkie statusy</SelectItem>
                <SelectItem value="sent">Wysłane</SelectItem>
                <SelectItem value="failed">Błędy</SelectItem>
                <SelectItem value="pending">Oczekujące</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex-1">
            <Select value={emailTypeFilter} onValueChange={setEmailTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filtruj po typie" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Wszystkie typy</SelectItem>
                <SelectItem value="audit">Audyty</SelectItem>
                <SelectItem value="notification">Powiadomienia</SelectItem>
                <SelectItem value="other">Inne</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Tabela historii */}
        {data && data.emailHistory.length > 0 ? (
          <>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Data wysyłki</TableHead>
                    <TableHead>Odbiorca</TableHead>
                    <TableHead>Temat</TableHead>
                    <TableHead>Typ</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Akcje</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.emailHistory.map((entry) => (
                    <TableRow key={entry.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">
                            {formatDate(entry.sentAt)}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm font-medium">
                            {entry.recipientEmail}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm" title={entry.subject}>
                          {entry.subject.length > 50
                            ? `${entry.subject.substring(0, 50)}...`
                            : entry.subject}
                        </span>
                      </TableCell>
                      <TableCell>{getEmailTypeBadge(entry.emailType)}</TableCell>
                      <TableCell>{getStatusBadge(entry.status)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center gap-2">
                          {/* Przycisk podglądu treści */}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewEmailContent(entry.id)}
                            disabled={!entry.hasContent || isLoadingContent}
                            title={entry.hasContent ? "Podgląd treści maila" : "Brak treści maila"}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>

                          {/* Przycisk usuwania */}
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                disabled={isDeleting === entry.id}
                                title="Usuń wpis z historii"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>
                                Usuń wpis z historii
                              </AlertDialogTitle>
                              <AlertDialogDescription>
                                Czy na pewno chcesz usunąć ten wpis z historii
                                maili? Ta akcja jest nieodwracalna.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Anuluj</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDeleteEntry(entry.id)}
                                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                              >
                                Usuń
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Paginacja */}
            {data.pagination.totalPages > 1 && (
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-muted-foreground">
                  Pokazano {data.emailHistory.length} z {data.pagination.totalCount} wpisów
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => fetchEmailHistory(currentPage - 1)}
                    disabled={!data.pagination.hasPrevPage || isLoading}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Poprzednia
                  </Button>
                  <span className="text-sm">
                    Strona {data.pagination.currentPage} z {data.pagination.totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => fetchEmailHistory(currentPage + 1)}
                    disabled={!data.pagination.hasNextPage || isLoading}
                  >
                    Następna
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <Mail className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Brak historii wysłanych maili</p>
            <p className="text-sm">
              {statusFilter !== "all" || emailTypeFilter !== "all"
                ? "Spróbuj zmienić filtry lub wyślij pierwszy mail audytowy"
                : "Wyślij pierwszy mail audytowy, aby zobaczyć historię"}
            </p>
          </div>
        )}
      </CardContent>

      {/* Modal podglądu treści maila */}
      <Dialog open={showContentModal} onOpenChange={setShowContentModal}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Podgląd wysłanego maila
            </DialogTitle>
            {selectedEmailContent && (
              <DialogDescription asChild>
                <div className="space-y-1 text-sm">
                  <div><strong>Do:</strong> {selectedEmailContent.recipientEmail}</div>
                  <div><strong>Temat:</strong> {selectedEmailContent.subject}</div>
                  <div><strong>Data wysyłki:</strong> {formatDate(selectedEmailContent.sentAt)}</div>
                  <div className="flex items-center gap-2">
                    <strong>Status:</strong> {getStatusBadge(selectedEmailContent.status)}
                  </div>
                </div>
              </DialogDescription>
            )}
          </DialogHeader>

          <ScrollArea className="max-h-[60vh] w-full">
            <div className="p-4">
              {selectedEmailContent?.emailContent ? (
                <div className="border rounded-lg">
                  <div className="bg-muted/50 px-4 py-2 border-b">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Treść maila (HTML)</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const newWindow = window.open('', '_blank');
                          if (newWindow && selectedEmailContent.emailContent) {
                            newWindow.document.write(selectedEmailContent.emailContent);
                            newWindow.document.close();
                          }
                        }}
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Otwórz w nowym oknie
                      </Button>
                    </div>
                  </div>
                  <div className="p-4">
                    <iframe
                      srcDoc={selectedEmailContent.emailContent}
                      className="w-full h-96 border-0"
                      title="Podgląd maila"
                      sandbox="allow-same-origin"
                    />
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Mail className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Brak treści maila</p>
                  <p className="text-sm">
                    Treść maila nie została zapisana lub wystąpił błąd podczas pobierania
                  </p>
                </div>
              )}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
