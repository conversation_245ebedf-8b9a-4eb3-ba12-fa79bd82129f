import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';
import EmailHistory from '@/models/EmailHistory';
import { sendAuditEmail } from '@/lib/email';

// POST endpoint to send audit email
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { id } = await params;
    const requestBody = await request.json();
    const { recipientEmail, subject } = requestBody;

    // Validate input
    if (!recipientEmail || !recipientEmail.trim()) {
      return NextResponse.json(
        {
          success: false,
          error: 'Recipient email is required'
        },
        { status: 400 }
      );
    }

    if (!subject || !subject.trim()) {
      return NextResponse.json(
        {
          success: false,
          error: 'Email subject is required'
        },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(recipientEmail)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid email format'
        },
        { status: 400 }
      );
    }

    // Find domain and verify ownership
    const domain = await Domain.findOne({
      _id: id,
      userId: session.user.id
    });

    if (!domain) {
      return NextResponse.json(
        {
          success: false,
          error: 'Domain not found or access denied'
        },
        { status: 404 }
      );
    }

    // Check if domain has audit content
    if (!domain.auditContent || domain.auditContent.trim().length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'No audit content available for this domain'
        },
        { status: 400 }
      );
    }

    // Create email history entry
    const emailHistory = new EmailHistory({
      domainId: domain._id,
      recipientEmail: recipientEmail.trim(),
      subject: subject.trim(),
      emailType: 'audit',
      status: 'pending',
      sentAt: new Date(),
      userId: session.user.id,
      metadata: {
        auditContentLength: domain.auditContent.length
      }
    });

    try {
      // Send email with tracking
      const emailResult = await sendAuditEmail(
        recipientEmail.trim(),
        subject.trim(),
        domain.auditContent,
        domain.domain,
        emailHistory._id.toString()
      );

      if (!emailResult.success) {
        // Update email history with failed status
        emailHistory.status = 'failed';
        emailHistory.metadata = {
          ...emailHistory.metadata,
          errorMessage: 'Failed to send email. Please check SMTP configuration.'
        };
        await emailHistory.save();

        return NextResponse.json(
          {
            success: false,
            error: 'Failed to send email. Please check SMTP configuration.'
          },
          { status: 500 }
        );
      }

      // Update email history with success status and save email content
      emailHistory.status = 'sent';
      emailHistory.emailContent = emailResult.htmlContent;
      await emailHistory.save();

      // Log the email sending activity
      console.log(`Audit email sent for domain ${domain.domain} to ${recipientEmail}`);

      return NextResponse.json({
        success: true,
        message: 'Audit email sent successfully',
        details: {
          domain: domain.domain,
          recipient: recipientEmail,
          subject: subject.trim(),
          sentAt: new Date().toISOString(),
          emailHistoryId: emailHistory._id
        }
      });

    } catch (emailError) {
      // Update email history with failed status
      emailHistory.status = 'failed';
      emailHistory.metadata = {
        ...emailHistory.metadata,
        errorMessage: emailError instanceof Error ? emailError.message : 'Unknown email error'
      };
      await emailHistory.save();

      throw emailError; // Re-throw to be caught by outer catch block
    }

  } catch (error) {
    console.error('Error sending audit email:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error occurred while sending email'
      },
      { status: 500 }
    );
  }
}
