import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Contact from '@/models/Contact';

// GET endpoint do eksportowania kontaktów
export async function GET(request: NextRequest) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'csv'; // csv, xml, excel
    const category = searchParams.get('category');

    await connectDB();

    // Buduj filtr - pokaż kontakty publiczne (userId = "0") oraz kontakty użytkownika
    const filter: {
      $or: Array<{ userId: string }>;
      contact_category?: { $regex: RegExp };
    } = {
      $or: [
        { userId: "0" }, // Kontakty publiczne
        { userId: session.user.id } // Kontakty użytkownika
      ]
    };

    if (category) {
      filter.contact_category = { $regex: new RegExp(`^${category}$`, 'i') };
    }

    // Pobierz wszystkie kontakty (bez paginacji dla eksportu)
    const contacts = await Contact.find(filter).sort({ createdAt: -1 });

    if (contacts.length === 0) {
      return NextResponse.json(
        { error: 'Brak kontaktów do eksportu' },
        { status: 404 }
      );
    }

    // Przygotuj dane do eksportu
    const exportData = contacts.map(contact => ({
      'Nazwa firmy': contact.contact_companyName,
      'Osoba kontaktowa': contact.contact_contactPerson,
      'Email': contact.contact_email,
      'Telefon': contact.contact_phone,
      'Kategoria': contact.contact_category,
      'Adres': contact.contact_address,
      'Notatki': contact.contact_notes,
      'Data utworzenia': new Date(contact.createdAt).toLocaleString('pl-PL'),
      'Data aktualizacji': new Date(contact.updatedAt).toLocaleString('pl-PL')
    }));

    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');

    if (format === 'csv') {
      // Eksport CSV
      const headers = Object.keys(exportData[0]);
      const csvContent = [
        headers.join(','),
        ...exportData.map(row =>
          headers.map(header => `"${row[header as keyof typeof row] || ''}"`).join(',')
        )
      ].join('\n');

      return new NextResponse(csvContent, {
        status: 200,
        headers: {
          'Content-Type': 'text/csv; charset=utf-8',
          'Content-Disposition': `attachment; filename="kontakty_${timestamp}.csv"`
        }
      });

    } else if (format === 'xml') {
      // Eksport XML
      const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<kontakty>
${exportData.map(contact => `  <kontakt>
    <nazwa_firmy>${escapeXml(contact['Nazwa firmy'])}</nazwa_firmy>
    <osoba_kontaktowa>${escapeXml(contact['Osoba kontaktowa'])}</osoba_kontaktowa>
    <email>${escapeXml(contact['Email'])}</email>
    <telefon>${escapeXml(contact['Telefon'])}</telefon>
    <kategoria>${escapeXml(contact['Kategoria'])}</kategoria>
    <adres>${escapeXml(contact['Adres'])}</adres>
    <notatki>${escapeXml(contact['Notatki'])}</notatki>
    <data_utworzenia>${escapeXml(contact['Data utworzenia'])}</data_utworzenia>
    <data_aktualizacji>${escapeXml(contact['Data aktualizacji'])}</data_aktualizacji>
  </kontakt>`).join('\n')}
</kontakty>`;

      return new NextResponse(xmlContent, {
        status: 200,
        headers: {
          'Content-Type': 'application/xml; charset=utf-8',
          'Content-Disposition': `attachment; filename="kontakty_${timestamp}.xml"`
        }
      });

    } else if (format === 'excel') {
      // Eksport Excel (jako CSV z odpowiednimi nagłówkami)
      const headers = Object.keys(exportData[0]);
      const csvContent = [
        '\uFEFF' + headers.join('\t'), // BOM dla Excel + tabulatory
        ...exportData.map(row =>
          headers.map(header => `"${row[header as keyof typeof row] || ''}"`).join('\t')
        )
      ].join('\n');

      return new NextResponse(csvContent, {
        status: 200,
        headers: {
          'Content-Type': 'application/vnd.ms-excel; charset=utf-8',
          'Content-Disposition': `attachment; filename="kontakty_${timestamp}.xls"`
        }
      });

    } else {
      return NextResponse.json(
        { error: 'Nieobsługiwany format eksportu. Dostępne formaty: csv, xml, excel' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Błąd eksportowania kontaktów:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas eksportowania kontaktów' },
      { status: 500 }
    );
  }
}

// Funkcja pomocnicza do escapowania XML
function escapeXml(unsafe: string): string {
  if (!unsafe) return '';
  return unsafe.replace(/[<>&'"]/g, function (c) {
    switch (c) {
      case '<': return '&lt;';
      case '>': return '&gt;';
      case '&': return '&amp;';
      case '\'': return '&apos;';
      case '"': return '&quot;';
      default: return c;
    }
  });
}
