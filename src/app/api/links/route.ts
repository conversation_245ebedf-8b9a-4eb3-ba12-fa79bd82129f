import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next'; // Changed import path
import { Session } from 'next-auth'; // Added Session import
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Link from '@/models/Link';

interface LinkFilter {
  userId: string;
  category?: { $regex: RegExp };
  query?: { $regex: string; $options: string };
  location?: { $regex: string; $options: string };
}

export async function GET(request: NextRequest) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null; // Explicitly cast session

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const query = searchParams.get('query');
    const location = searchParams.get('location');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');

    await connectDB();

    // Buduj filtr
    const filter: LinkFilter = { userId: session.user.id };

    if (category) {
      filter.category = { $regex: new RegExp(`^${category}$`, 'i') };
    }

    if (query) {
      filter.query = { $regex: query, $options: 'i' };
    }

    if (location) {
      filter.location = { $regex: location, $options: 'i' };
    }

    // Pobierz linki z paginacją
    const links = await Link.find(filter)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit);

    // Policz całkowitą liczbę linków
    const totalCount = await Link.countDocuments(filter);

    // Pobierz unikalne kategorie użytkownika
    const categories = await Link.distinct('category', { userId: session.user.id });

    return NextResponse.json({
      success: true,
      links: links.map(link => ({
        id: link._id,
        url: link.url,
        title: link.title,
        snippet: link.snippet,
        category: link.category,
        query: link.query,
        createdAt: link.createdAt
      })),
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page * limit < totalCount,
        hasPrev: page > 1
      },
      categories
    });

  } catch (error) {
    console.error('Błąd pobierania linków:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas pobierania linków' },
      { status: 500 }
    );
  }
}
