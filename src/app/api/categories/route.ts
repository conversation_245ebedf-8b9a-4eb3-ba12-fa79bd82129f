import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Category from '@/models/Category';

export async function GET() {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    // Pobierz kategorie użytkownika
    const categories = await Category.find({ userId: session.user.id })
      .sort({ name: 1 });

    return NextResponse.json({
      success: true,
      categories: categories.map(category => ({
        id: category._id,
        name: category.name,
        description: category.description,
        createdAt: category.createdAt,
        updatedAt: category.updatedAt
      }))
    });

  } catch (error) {
    console.error('Błąd pobierania kategorii:', error);
    return NextResponse.json(
      { error: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> błąd podczas pobierania kategorii' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { name, description } = await request.json();

    if (!name || name.trim().length === 0) {
      return NextResponse.json(
        { error: 'Nazwa kategorii jest wymagana' },
        { status: 400 }
      );
    }

    await connectDB();

    // Sprawdź czy kategoria już istnieje
    const existingCategory = await Category.findOne({
      userId: session.user.id,
      name: name.trim()
    });

    if (existingCategory) {
      return NextResponse.json(
        { error: 'Kategoria o tej nazwie już istnieje' },
        { status: 400 }
      );
    }

    // Utwórz nową kategorię
    const category = new Category({
      name: name.trim(),
      description: description?.trim() || '',
      userId: session.user.id
    });

    await category.save();

    return NextResponse.json({
      success: true,
      category: {
        id: category._id,
        name: category.name,
        description: category.description,
        createdAt: category.createdAt,
        updatedAt: category.updatedAt
      }
    });

  } catch (error) {
    console.error('Błąd tworzenia kategorii:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas tworzenia kategorii' },
      { status: 500 }
    );
  }
}
