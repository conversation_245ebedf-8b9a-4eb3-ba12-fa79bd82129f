import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next'; // Changed import path
import { Session } from 'next-auth'; // Added Session import
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import EmailTracking from '@/models/EmailTracking';
import EmailHistory from '@/models/EmailHistory';

interface EmailTrackingQuery {
  createdAt: { $gte: Date };
  domainId?: string;
}

// GET endpoint to fetch email tracking statistics
export async function GET(request: NextRequest) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null; // Explicitly cast session

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { searchParams } = new URL(request.url);
    const domainId = searchParams.get('domainId');
    const days = parseInt(searchParams.get('days') || '30');

    // Oblicz datę początkową
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Buduj query
    const query: EmailTrackingQuery = {
      createdAt: { $gte: startDate }
    };

    if (domainId) {
      query.domainId = domainId;
    }

    // Pobierz statystyki eventów
    const eventStats = await EmailTracking.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$eventType',
          count: { $sum: 1 }
        }
      }
    ]);

    // Pobierz statystyki dzienne
    const dailyStats = await EmailTracking.aggregate([
      { $match: query },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
            eventType: '$eventType'
          },
          count: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: '$_id.date',
          events: {
            $push: {
              eventType: '$_id.eventType',
              count: '$count'
            }
          }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Pobierz statystyki dla najczęściej klikanych linków
    const linkStats = await EmailTracking.aggregate([
      {
        $match: {
          ...query,
          eventType: 'clicked',
          'eventData.link': { $exists: true, $ne: null }
        }
      },
      {
        $group: {
          _id: '$eventData.link',
          count: { $sum: 1 },
          recipients: { $addToSet: '$recipientEmail' }
        }
      },
      {
        $project: {
          link: '$_id',
          clickCount: '$count',
          uniqueRecipients: { $size: '$recipients' }
        }
      },
      { $sort: { clickCount: -1 } },
      { $limit: 10 }
    ]);

    // Pobierz statystyki dla domen (jeśli nie filtrujemy po konkretnej domenie)
    let domainStats = [];
    if (!domainId) {
      domainStats = await EmailTracking.aggregate([
        { $match: query },
        {
          $group: {
            _id: '$domainId',
            totalEvents: { $sum: 1 },
            events: {
              $push: {
                eventType: '$eventType',
                count: 1
              }
            }
          }
        },
        {
          $project: {
            domainId: '$_id',
            totalEvents: 1,
            eventBreakdown: {
              $reduce: {
                input: '$events',
                initialValue: {},
                in: {
                  $mergeObjects: [
                    '$$value',
                    {
                      $arrayToObject: [[{
                        k: '$$this.eventType',
                        v: { $add: [{ $ifNull: [{ $getField: { field: '$$this.eventType', input: '$$value' } }, 0] }, 1] }
                      }]]
                    }
                  ]
                }
              }
            }
          }
        },
        { $sort: { totalEvents: -1 } },
        { $limit: 10 }
      ]);
    }

    // Pobierz ogólne statystyki
    const totalEmails = await EmailHistory.countDocuments(
      domainId ? { domainId } : {}
    );

    const totalTrackedEvents = await EmailTracking.countDocuments(query);

    // Oblicz wskaźniki
    const openRate = eventStats.find(stat => stat._id === 'opened')?.count || 0;
    const clickRate = eventStats.find(stat => stat._id === 'clicked')?.count || 0;
    const sentCount = eventStats.find(stat => stat._id === 'sent')?.count || totalEmails;

    const openRatePercentage = sentCount > 0 ? ((openRate / sentCount) * 100).toFixed(2) : '0.00';
    const clickRatePercentage = sentCount > 0 ? ((clickRate / sentCount) * 100).toFixed(2) : '0.00';

    return NextResponse.json({
      success: true,
      data: {
        summary: {
          totalEmails,
          totalTrackedEvents,
          openRate: parseFloat(openRatePercentage),
          clickRate: parseFloat(clickRatePercentage),
          sentCount,
          openCount: openRate,
          clickCount: clickRate
        },
        eventStats: eventStats.reduce((acc, stat) => {
          acc[stat._id] = stat.count;
          return acc;
        }, {} as Record<string, number>),
        dailyStats,
        linkStats,
        domainStats,
        period: {
          days,
          startDate: startDate.toISOString(),
          endDate: new Date().toISOString()
        }
      }
    });

  } catch (error) {
    console.error('Error fetching email tracking statistics:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
