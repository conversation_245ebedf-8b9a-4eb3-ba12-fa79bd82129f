"use client";

import { useState, useEffect, useCallback } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  // AlertDialogTrigger, // Trigger will be the existing button
} from "@/components/ui/alert-dialog";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import {
  Link as LinkIcon,
  Globe,
  Search,
  Filter,
  ChevronLeft,
  ChevronRight,
  ExternalLink,
  ArrowUpDown,
  Trash2,
} from "lucide-react";

interface Link {
  id: string;
  url: string;
  title: string;
  snippet?: string;
  category: string;
  query: string;
  location?: string;
  createdAt: string;
}

interface LinksResponse {
  success: boolean;
  links: Link[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  categories: string[];
}

export default function LinksPage() {
  const { status } = useSession();
  const [links, setLinks] = useState<Link[]>([]);
  const [pagination, setPagination] = useState<
    LinksResponse["pagination"] | null
  >(null);
  const [categories, setCategories] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Filtry
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [locationFilter, setLocationFilter] = useState("");
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [linkToDelete, setLinkToDelete] = useState<{ id: string; title: string } | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState("createdAt");
  const [sortOrder, setSortOrder] = useState("desc");
  const [showFilters, setShowFilters] = useState(false);

  const fetchLinks = useCallback(async (page = 1, category = "all", query = "", location = "") => {
    // Sprawdź czy użytkownik jest zalogowany
    if (status === "loading") return;
    if (status === "unauthenticated") {
      toast.error("Musisz być zalogowany aby zobaczyć linki");
      return;
    }

    setIsLoading(true);

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "20",
      });

      if (category && category !== "all") params.append("category", category);
      if (query) params.append("query", query);
      if (location) params.append("location", location);

      const response = await fetch(`/api/links?${params}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include", // Ważne: dołącz cookies sesji
      });

      if (!response.ok) {
        if (response.status === 401) {
          toast.error("Sesja wygasła. Zaloguj się ponownie.");
          return;
        }
        throw new Error("Błąd pobierania linków");
      }

      const data: LinksResponse = await response.json();
      setLinks(data.links);
      setPagination(data.pagination);
      setCategories(data.categories);
    } catch (error) {
      console.error("Błąd pobierania linków:", error);
      toast.error("Wystąpił błąd podczas pobierania linków");
    } finally {
      setIsLoading(false);
    }
  }, [status]); // Removed unnecessary dependencies: currentPage, selectedCategory, searchQuery are passed as parameters

  useEffect(() => {
    if (status === "authenticated") {
      fetchLinks(currentPage, selectedCategory, searchQuery, locationFilter);
    }
  }, [status, fetchLinks, currentPage, selectedCategory, searchQuery, locationFilter]);

  const handleSearch = () => {
    setCurrentPage(1); // The useEffect will trigger fetchLinks due to currentPage change
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const promptDeleteLink = (linkId: string, linkTitle: string) => {
    setLinkToDelete({ id: linkId, title: linkTitle });
    setIsDeleteDialogOpen(true);
  };

  const executeDeleteLink = async () => {
    if (!linkToDelete) return;

    try {
      const response = await fetch(`/api/links/${linkToDelete.id}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
      });

      if (!response.ok) {
        if (response.status === 401) {
          toast.error("Sesja wygasła. Zaloguj się ponownie.");
          return;
        }
        throw new Error("Błąd usuwania linku");
      }

      const data = await response.json();

      if (data.success) {
        toast.success(`Link "${linkToDelete.title}" został usunięty`);
        fetchLinks(currentPage, selectedCategory, searchQuery, locationFilter); // Refresh list
      } else {
        throw new Error(data.error || "Błąd usuwania linku");
      }
    } catch (error) {
      console.error("Błąd usuwania linku:", error);
      toast.error("Wystąpił błąd podczas usuwania linku");
    } finally {
      setIsDeleteDialogOpen(false);
      setLinkToDelete(null);
    }
  };

  // Pokaż loading podczas ładowania sesji
  if (status === "loading") {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Ładowanie...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Przekieruj jeśli nie zalogowany (middleware powinien to obsłużyć, ale dla pewności)
  if (status === "unauthenticated") {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground">
              Musisz być zalogowany aby zobaczyć linki.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Nagłówek */}
      <div>
        <h1 className="text-3xl font-bold text-foreground">Zapisane Linki</h1>
        <p className="text-muted-foreground mt-2">
          Przeglądaj wszystkie linki zebrane z wyszukiwań Serper API
        </p>
      </div>

      {/* Toolbar */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            {showFilters ? "Ukryj filtry" : "Pokaż filtry"}
          </Button>
        </div>
      </div>

      {/* Filtry */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filtry i sortowanie
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div className="space-y-2">
                <Label htmlFor="search">Wyszukaj w zapytaniach</Label>
                <div className="flex gap-2">
                  <Input
                    id="search"
                    placeholder="Wyszukaj..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                  />
                  <Button onClick={handleSearch} size="sm">
                    <Search className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="category">Kategoria</Label>
                <Select
                  value={selectedCategory}
                  onValueChange={handleCategoryChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Wszystkie kategorie" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Wszystkie kategorie</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="location">Lokalizacja</Label>
                <Input
                  id="location"
                  placeholder="np. Warsaw, Poland"
                  value={locationFilter}
                  onChange={(e) => setLocationFilter(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                />
              </div>

              <div className="space-y-2">
                <Label>Sortuj według</Label>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="createdAt">Data utworzenia</SelectItem>
                    <SelectItem value="title">Tytuł</SelectItem>
                    <SelectItem value="url">URL</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Kolejność</Label>
                <Select value={sortOrder} onValueChange={setSortOrder}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="desc">Malejąco</SelectItem>
                    <SelectItem value="asc">Rosnąco</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Tabela linków */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <LinkIcon className="h-5 w-5" />
            Zapisane linki
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-muted-foreground">Ładowanie linków...</p>
            </div>
          ) : links.length === 0 ? (
            <div className="p-8 text-center">
              <LinkIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">
                Brak linków
              </h3>
              <p className="text-muted-foreground">
                Nie znaleziono linków spełniających kryteria wyszukiwania.
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]">
                    <Button variant="ghost" size="sm" className="h-8 p-0">
                      <ArrowUpDown className="h-4 w-4" />
                    </Button>
                  </TableHead>
                  <TableHead>Tytuł</TableHead>
                  <TableHead>Kategoria</TableHead>
                  <TableHead>Zapytanie</TableHead>
                  <TableHead>Lokalizacja</TableHead>
                  <TableHead>Domena</TableHead>
                  <TableHead>Data</TableHead>
                  <TableHead className="w-[100px]">Akcje</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {links.map((link) => (
                  <TableRow key={link.id}>
                    <TableCell>
                      <div className="w-4 h-4 rounded-full bg-primary/20"></div>
                    </TableCell>
                    <TableCell className="max-w-[300px]">
                      <div className="space-y-1">
                        <div className="font-medium text-foreground truncate">
                          {link.title}
                        </div>
                        {link.snippet && (
                          <div className="text-xs text-muted-foreground line-clamp-2">
                            {link.snippet}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary" className="text-xs">
                        {link.category}
                      </Badge>
                    </TableCell>
                    <TableCell className="max-w-[200px]">
                      <div className="text-sm text-muted-foreground truncate">
                        {link.query}
                      </div>
                    </TableCell>
                    <TableCell className="max-w-[150px]">
                      <div className="text-sm text-muted-foreground truncate">
                        {link.location || "-"}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Globe className="h-3 w-3" />
                        <span className="truncate max-w-[150px]">
                          {new URL(link.url).hostname}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm text-muted-foreground">
                        {new Date(link.createdAt).toLocaleDateString("pl-PL", {
                          year: "numeric",
                          month: "short",
                          day: "numeric",
                        })}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          asChild
                        >
                          <a
                            href={link.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="hover:bg-accent hover:text-accent-foreground"
                          >
                            <ExternalLink className="h-4 w-4" />
                          </a>
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 hover:bg-destructive hover:text-destructive-foreground"
                          onClick={() =>
                            promptDeleteLink(link.id, link.title || link.url)
                          }
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* AlertDialog for Delete Confirmation */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
          <AlertDialogTitle>Potwierdzenie usunięcia</AlertDialogTitle>
          <AlertDialogDescription>
            Czy na pewno chcesz usunąć link <strong>{linkToDelete?.title}</strong>?
            Tej operacji nie można cofnąć.
          </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setLinkToDelete(null)}>Anuluj</AlertDialogCancel>
            <AlertDialogAction onClick={executeDeleteLink} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Usuń
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Paginacja */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Strona {pagination.page} z {pagination.totalPages} (
            {pagination.totalCount} linków)
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={!pagination.hasPrev}
              className="hover:bg-accent hover:text-accent-foreground transition-colors"
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              Poprzednia
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={!pagination.hasNext}
              className="hover:bg-accent hover:text-accent-foreground transition-colors"
            >
              Następna
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
