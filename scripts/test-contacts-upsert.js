// Skrypt do testowania funkcjonalności upsert dla kontaktów
// Uruchom: node scripts/test-contacts-upsert.js

const fs = require("fs");
const path = require("path");

// Funkcja do odczytania zmiennych środowiskowych z .env.local
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, "..", ".env.local");
    const envContent = fs.readFileSync(envPath, "utf8");
    const lines = envContent.split("\n");

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith("#")) {
        const [key, ...valueParts] = trimmedLine.split("=");
        if (key && valueParts.length > 0) {
          process.env[key.trim()] = valueParts.join("=").trim();
        }
      }
    }
  } catch (error) {
    console.error("Nie można odczyta<PERSON> pliku .env.local:", error.message);
  }
}

loadEnvFile();

async function testContactsUpsert() {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  
  console.log("🧪 Testowanie funkcjonalności upsert dla kontaktów");
  console.log(`📍 URL: ${baseUrl}/api/contacts`);

  const testContact = {
    contact_companyName: "Test Company Sp. z o.o.",
    contact_contactPerson: "Jan Kowalski",
    contact_email: "<EMAIL>",
    contact_phone: "+48 ***********",
    contact_category: "Test",
    contact_address: "ul. Testowa 123, 00-000 Warszawa",
    contact_notes: "Kontakt testowy - pierwszy"
  };

  const updatedContact = {
    ...testContact,
    contact_companyName: "Updated Test Company Sp. z o.o.",
    contact_contactPerson: "Anna Nowak",
    contact_phone: "+48 ***********",
    contact_notes: "Kontakt testowy - zaktualizowany"
  };

  try {
    console.log("\n📤 Test 1: Tworzenie nowego kontaktu");
    const response1 = await fetch(`${baseUrl}/api/contacts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testContact)
    });

    const result1 = await response1.json();
    console.log(`Status: ${response1.status}`);
    console.log(`Odpowiedź:`, result1);

    if (!response1.ok) {
      console.error("❌ Błąd podczas tworzenia kontaktu");
      return;
    }

    const contactId1 = result1.contact?.id;
    console.log(`✅ Kontakt utworzony z ID: ${contactId1}`);

    console.log("\n📤 Test 2: Aktualizacja tego samego kontaktu (ten sam email)");
    const response2 = await fetch(`${baseUrl}/api/contacts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updatedContact)
    });

    const result2 = await response2.json();
    console.log(`Status: ${response2.status}`);
    console.log(`Odpowiedź:`, result2);

    if (!response2.ok) {
      console.error("❌ Błąd podczas aktualizacji kontaktu");
      return;
    }

    const contactId2 = result2.contact?.id;
    console.log(`✅ Kontakt zaktualizowany z ID: ${contactId2}`);

    // Sprawdź czy ID się nie zmieniło (to znaczy że został zaktualizowany, a nie utworzony nowy)
    if (contactId1 === contactId2) {
      console.log("✅ SUKCES: Kontakt został zaktualizowany (to samo ID)");
    } else {
      console.log("⚠️  UWAGA: Utworzono nowy kontakt zamiast aktualizować istniejący");
      console.log(`   Pierwsze ID: ${contactId1}`);
      console.log(`   Drugie ID: ${contactId2}`);
    }

    // Sprawdź czy dane zostały zaktualizowane
    if (result2.contact?.contact_companyName === updatedContact.contact_companyName) {
      console.log("✅ SUKCES: Dane zostały zaktualizowane");
    } else {
      console.log("❌ BŁĄD: Dane nie zostały zaktualizowane");
    }

    console.log("\n📤 Test 3: Próba dodania kontaktu z innym emailem");
    const newContact = {
      ...testContact,
      contact_email: "<EMAIL>",
      contact_companyName: "Inna Firma Sp. z o.o."
    };

    const response3 = await fetch(`${baseUrl}/api/contacts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(newContact)
    });

    const result3 = await response3.json();
    console.log(`Status: ${response3.status}`);
    console.log(`Odpowiedź:`, result3);

    if (response3.ok) {
      const contactId3 = result3.contact?.id;
      console.log(`✅ Nowy kontakt utworzony z ID: ${contactId3}`);
      
      if (contactId3 !== contactId1 && contactId3 !== contactId2) {
        console.log("✅ SUKCES: Nowy kontakt ma inne ID (poprawnie utworzony)");
      }
    }

    console.log("\n🎉 Test zakończony pomyślnie!");
    console.log("\n📋 Podsumowanie:");
    console.log("- Pierwszy POST: tworzy nowy kontakt");
    console.log("- Drugi POST (ten sam email): aktualizuje istniejący kontakt");
    console.log("- Trzeci POST (inny email): tworzy nowy kontakt");

  } catch (error) {
    console.error("❌ Błąd podczas testowania:", error);
  }
}

// Sprawdź czy serwer jest uruchomiony
async function checkServer() {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  
  try {
    const response = await fetch(`${baseUrl}/api/contacts`);
    return response.status !== 404;
  } catch (error) {
    return false;
  }
}

async function main() {
  console.log("🔍 Sprawdzanie czy serwer jest uruchomiony...");
  
  const serverRunning = await checkServer();
  if (!serverRunning) {
    console.error("❌ Serwer nie jest uruchomiony lub endpoint /api/contacts nie istnieje");
    console.log("💡 Uruchom serwer: npm run dev");
    process.exit(1);
  }

  console.log("✅ Serwer jest uruchomiony");
  await testContactsUpsert();
}

main();
