import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Category from '@/models/Category';

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { name, description } = await request.json();

    if (!name || name.trim().length === 0) {
      return NextResponse.json(
        { error: 'Nazwa kategorii jest wymagana' },
        { status: 400 }
      );
    }

    await connectDB();

    const { id } = await params;

    // Sprawdź czy kategoria istnieje i należy do użytkownika
    const category = await Category.findOne({
      _id: id,
      userId: session.user.id
    });

    if (!category) {
      return NextResponse.json(
        { error: 'Kategoria nie została znaleziona' },
        { status: 404 }
      );
    }

    // Sprawdź czy nowa nazwa nie koliduje z istniejącą kategorią
    if (name.trim() !== category.name) {
      const existingCategory = await Category.findOne({
        userId: session.user.id,
        name: name.trim(),
        _id: { $ne: id }
      });

      if (existingCategory) {
        return NextResponse.json(
          { error: 'Kategoria o tej nazwie już istnieje' },
          { status: 400 }
        );
      }
    }

    // Aktualizuj kategorię
    category.name = name.trim();
    category.description = description?.trim() || '';
    await category.save();

    return NextResponse.json({
      success: true,
      category: {
        id: category._id,
        name: category.name,
        description: category.description,
        createdAt: category.createdAt,
        updatedAt: category.updatedAt
      }
    });

  } catch (error) {
    console.error('Błąd aktualizacji kategorii:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas aktualizacji kategorii' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { id } = await params;

    // Sprawdź czy kategoria istnieje i należy do użytkownika
    const category = await Category.findOne({
      _id: id,
      userId: session.user.id
    });

    if (!category) {
      return NextResponse.json(
        { error: 'Kategoria nie została znaleziona' },
        { status: 404 }
      );
    }



    // Usuń kategorię
    await Category.findByIdAndDelete(id);

    return NextResponse.json({
      success: true,
      message: 'Kategoria została usunięta'
    });

  } catch (error) {
    console.error('Błąd usuwania kategorii:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas usuwania kategorii' },
      { status: 500 }
    );
  }
}
