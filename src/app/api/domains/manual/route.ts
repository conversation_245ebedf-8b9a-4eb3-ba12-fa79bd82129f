import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next'; // Changed import path
import { Session } from 'next-auth'; // Added Session import
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';
import { checkDomainCms } from '@/lib/cms-checker';

export async function POST(request: NextRequest) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null; // Explicitly cast session

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { url, category } = await request.json();

    if (!url || !category) {
      return NextResponse.json(
        { error: 'URL i kategoria są wymagane' },
        { status: 400 }
      );
    }

    await connectDB();

    // Parsuj URL
    let parsedUrl;
    try {
      // <PERSON><PERSON><PERSON> protokół jeśli go brak
      const urlWithProtocol = url.startsWith('http://') || url.startsWith('https://')
        ? url
        : `https://${url}`;

      parsedUrl = new URL(urlWithProtocol);
    } catch (_error) {
      console.log(_error)
      return NextResponse.json(
        { error: 'Nieprawidłowy format URL' },
        { status: 400 }
      );
    }

    const protocol = parsedUrl.protocol.replace(':', '');
    const domain = parsedUrl.hostname;
    const fullDomain = `${protocol}://${domain}`;

    // Sprawdź czy domena już istnieje w systemie dla tego użytkownika (globalnie)
    const existingDomain = await Domain.findOne({
      userId: session.user.id,
      fullDomain
    });

    if (existingDomain) {
      return NextResponse.json(
        {
          error: `Ta domena już istnieje w systemie w kategorii "${existingDomain.category}"`,
          existingCategory: existingDomain.category,
          existingDomainId: existingDomain._id
        },
        { status: 400 }
      );
    }

    // Sprawdź CMS dla nowej domeny
    const cmsCheckResult = await checkDomainCms(fullDomain);
    const cmsType = cmsCheckResult.cms;

    // Utwórz nową domenę z informacją o CMS
    const newDomain = new Domain({
      domain,
      protocol,
      fullDomain,
      category: category.trim(),
      userId: session.user.id,
      linkCount: 1, // Domyślnie 1 dla ręcznie dodanych domen
      cms: cmsType
    });

    await newDomain.save();

    return NextResponse.json({
      success: true,
      domain: {
        id: newDomain._id,
        domain: newDomain.domain,
        protocol: newDomain.protocol,
        fullDomain: newDomain.fullDomain,
        category: newDomain.category,
        linkCount: newDomain.linkCount,
        cms: newDomain.cms,
        createdAt: newDomain.createdAt,
        updatedAt: newDomain.updatedAt
      },
      message: `Domena została dodana pomyślnie${cmsType === 'wordpress' ? ' (wykryto WordPress)' : ' (CMS: inne)'}`
    });

  } catch (error) {
    console.error('Błąd dodawania domeny:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas dodawania domeny' },
      { status: 500 }
    );
  }
}
