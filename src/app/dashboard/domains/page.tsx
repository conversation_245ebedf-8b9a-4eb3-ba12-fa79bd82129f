"use client";

import { useState, useEffect, Suspense, useCallback, useRef } from "react";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import {
  Globe,
  Search,
  Filter,
  ChevronLeft,
  ChevronRight,
  ExternalLink,
  Calendar,
  Code,
  Server,
  Ban,
  CheckCircle,
  Clock,
  AlertCircle,
  XCircle,
  Phone,
} from "lucide-react";

interface Domain {
  id: string;
  domain: string;
  protocol: string;
  fullDomain: string;
  category: string;
  linkCount: number;
  status:
    | "new"
    | "accepted"
    | "rejected"
    | "closed"
    | "in_progress"
    | "in_audit"
    | "production"
    | "hold";
  cms?: string;
  ocena?: "wysoka" | "niska" | "brak";
  auditContent?: string;
  lighthouseContent?: string;
  metadata?: Record<string, string | number | boolean | null>; // Keeping as any for now due to dynamic nature
  createdAt: string;
  updatedAt: string;
}

interface TabFilterState {
  searchTerm: string;
  selectedCategory: string;
  selectedStatus: string;
  selectedOcena: string;
}

interface TabFilters {
  wordpress: TabFilterState;
  inny: TabFilterState;
  "not-checked": TabFilterState;
}

interface DomainsResponse {
  success: boolean;
  domains: Domain[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  stats: Record<string, { totalDomains: number; totalLinks: number }>;
  statusStats: {
    totalDomains: number;
    verifiedDomains: number;
    rejectedDomains: number;
    unprocessedDomains: number;
    inProgressDomains: number;
    closedDomains: number;
    holdDomains: number;
  };
  categories: string[];
}

function DomainsPageContent() {
  const searchParams = useSearchParams();

  const [domains, setDomains] = useState<Domain[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [pagination, setPagination] = useState({
    page: 1,
    totalPages: 1,
    totalCount: 0,
    hasNext: false,
    hasPrev: false,
  });
  const [categories, setCategories] = useState<string[]>([]);
  const [, setStatusStats] = useState({
    totalDomains: 0,
    verifiedDomains: 0,
    rejectedDomains: 0,
    unprocessedDomains: 0,
    inProgressDomains: 0,
    closedDomains: 0,
    holdDomains: 0,
  });

  // Filtry
  const [activeCmsTab, setActiveCmsTab] = useState("wordpress");
  const [sortBy, setSortBy] = useState("updatedAt");
  const [sortOrder, setSortOrder] = useState("desc");
  const [showFilters, setShowFilters] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);
  const hasInitialized = useRef(false);

  // Separate filter states for each CMS tab
  const [tabFilters, setTabFilters] = useState({
    wordpress: {
      searchTerm: "",
      selectedCategory: "all-categories",
      selectedStatus: "all-statuses",
      selectedOcena: "all-oceny",
    },
    inny: {
      searchTerm: "",
      selectedCategory: "all-categories",
      selectedStatus: "all-statuses",
      selectedOcena: "all-oceny",
    },
    "not-checked": {
      searchTerm: "",
      selectedCategory: "all-categories",
      selectedStatus: "all-statuses",
      selectedOcena: "all-oceny",
    },
  });

  const saveFiltersToStorage = (cmsType: string, filters: TabFilterState) => {
    if (typeof window !== "undefined") {
      localStorage.setItem(
        `domains-filters-${cmsType}`,
        JSON.stringify(filters)
      );
    }
  };

  const loadFiltersFromStorage = (cmsType: string): TabFilterState | null => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem(`domains-filters-${cmsType}`);
      if (saved) {
        try {
          return JSON.parse(saved);
        } catch (e) {
          console.error("Error parsing saved filters:", e);
        }
      }
    }
    return null;
  };

  const fetchDomains = useCallback(async (page = 1, cmsType = activeCmsTab) => {
    setIsLoading(true);
    try {
      const currentFilters = tabFilters[cmsType as keyof typeof tabFilters];
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "20",
        sortBy,
        sortOrder,
        cms: cmsType,
      });

      if (currentFilters.searchTerm)
        params.append("search", currentFilters.searchTerm);
      if (
        currentFilters.selectedCategory &&
        currentFilters.selectedCategory !== "all-categories"
      ) {
        params.append("category", currentFilters.selectedCategory);
      }
      if (
        currentFilters.selectedStatus &&
        currentFilters.selectedStatus !== "all-statuses"
      ) {
        params.append("status", currentFilters.selectedStatus);
      }
      if (
        currentFilters.selectedOcena &&
        currentFilters.selectedOcena !== "all-oceny"
      ) {
        params.append("ocena", currentFilters.selectedOcena);
      }

      const response = await fetch(`/api/domains?${params}`);

      if (!response.ok) {
        throw new Error("Błąd pobierania domen");
      }

      const data: DomainsResponse = await response.json();
      setDomains(data.domains);
      setPagination({
        page: data.pagination.page,
        totalPages: data.pagination.totalPages,
        totalCount: data.pagination.totalCount,
        hasNext: data.pagination.hasNext,
        hasPrev: data.pagination.hasPrev,
      });
      setCategories(data.categories);
      setStatusStats(data.statusStats);
    } catch (error) {
      console.error("Błąd pobierania domen:", error);
      toast.error("Wystąpił błąd podczas pobierania domen");
    } finally {
      setIsLoading(false);
    }
  }, [
    activeCmsTab,
    sortBy,
    sortOrder,
    tabFilters,
  ]);

  // Inicjalizacja przy pierwszym załadowaniu
  useEffect(() => {
    if (hasInitialized.current) return;

    const urlPage = searchParams.get("page");
    const urlCms = searchParams.get("cms");
    const urlSearch = searchParams.get("search");
    const urlCategory = searchParams.get("category");
    const urlStatus = searchParams.get("status");
    const urlSortBy = searchParams.get("sortBy");
    const urlSortOrder = searchParams.get("sortOrder");

    // Ustaw aktywną zakładkę CMS
    const activeCms =
      urlCms && ["wordpress", "inny", "not-checked"].includes(urlCms)
        ? urlCms
        : "wordpress";
    setActiveCmsTab(activeCms);

    // Ustaw sortowanie
    if (urlSortBy) setSortBy(urlSortBy);
    if (urlSortOrder) setSortOrder(urlSortOrder);

    // Załaduj filtry z localStorage dla każdej zakładki
    const updatedTabFilters: TabFilters = {
      wordpress: {
        searchTerm: "",
        selectedCategory: "all-categories",
        selectedStatus: "all-statuses",
        selectedOcena: "all-oceny",
      },
      inny: {
        searchTerm: "",
        selectedCategory: "all-categories",
        selectedStatus: "all-statuses",
        selectedOcena: "all-oceny",
      },
      "not-checked": {
        searchTerm: "",
        selectedCategory: "all-categories",
        selectedStatus: "all-statuses",
        selectedOcena: "all-oceny",
      },
    };

    ["wordpress", "inny", "not-checked"].forEach((cmsType) => {
      const savedFilters = loadFiltersFromStorage(cmsType);
      if (savedFilters) {
        updatedTabFilters[cmsType as keyof typeof updatedTabFilters] =
          savedFilters;
      }
    });

    // Nadpisz filtry z URL dla aktywnej zakładki
    if (urlSearch || urlCategory || urlStatus) {
      updatedTabFilters[activeCms as keyof typeof updatedTabFilters] = {
        searchTerm:
          urlSearch ||
          updatedTabFilters[activeCms as keyof typeof updatedTabFilters]
            .searchTerm,
        selectedCategory:
          urlCategory ||
          updatedTabFilters[activeCms as keyof typeof updatedTabFilters]
            .selectedCategory,
        selectedStatus:
          urlStatus ||
          updatedTabFilters[activeCms as keyof typeof updatedTabFilters]
            .selectedStatus,
        selectedOcena:
          updatedTabFilters[activeCms as keyof typeof updatedTabFilters]
            .selectedOcena,
      };
    }

    setTabFilters(updatedTabFilters);

    // Pobierz domeny z odpowiednią stroną
    const pageNumber = urlPage ? parseInt(urlPage) : 1;

    // Oznacz jako zainicjalizowany
    hasInitialized.current = true;

    // Użyj setTimeout aby uniknąć problemów z synchronizacją stanu
    setTimeout(() => {
      fetchDomains(pageNumber, activeCms);
    }, 0);
  }, [fetchDomains, searchParams]); // Empty dependency array - run only once

  // Reaguj na zmiany filtrów i sortowania (ale nie przy pierwszym załadowaniu)
  useEffect(() => {
    if (isInitialized) {
      // Tylko jeśli już zostaliśmy zainicjalizowani
      fetchDomains(1, activeCmsTab);
    }
  }, [tabFilters, sortBy, sortOrder, activeCmsTab, isInitialized, fetchDomains]);

  // Oznacz jako zainicjalizowany po pierwszym załadowaniu
  useEffect(() => {
    if (domains.length > 0 && !isInitialized) {
      setIsInitialized(true);
    }
  }, [domains.length, isInitialized]);

  const handlePageChange = (newPage: number) => {
    fetchDomains(newPage, activeCmsTab);
  };

  const handleTabChange = (newTab: string) => {
    setActiveCmsTab(newTab);
    // Pobierz domeny dla nowej zakładki od strony 1
    fetchDomains(1, newTab);
  };

  const updateTabFilter = (
    cmsType: string,
    filterType: "searchTerm" | "selectedCategory" | "selectedStatus" | "selectedOcena",
    value: string
  ) => {
    const newFilters = {
      ...tabFilters,
      [cmsType]: {
        ...tabFilters[cmsType as keyof typeof tabFilters],
        [filterType]: value,
      },
    };

    setTabFilters(newFilters);

    // Zapisz filtry do localStorage
    saveFiltersToStorage(
      cmsType,
      newFilters[cmsType as keyof typeof newFilters]
    );
  };

  const getCmsTabLabel = (cmsType: string) => {
    switch (cmsType) {
      case "wordpress":
        return "WordPress";
      case "inny":
        return "Inny";
      case "not-checked":
        return "Nie sprawdzono";
      default:
        return cmsType;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("pl-PL", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Funkcja renderowania numerów stron
  const renderPaginationNumbers = () => {
    const { page: currentPage, totalPages } = pagination;

    if (totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;

    let startPage, endPage;

    if (totalPages <= maxVisiblePages) {
      startPage = 1;
      endPage = totalPages;
    } else if (currentPage <= 3) {
      startPage = 1;
      endPage = maxVisiblePages;
    } else if (currentPage >= totalPages - 2) {
      startPage = totalPages - maxVisiblePages + 1;
      endPage = totalPages;
    } else {
      startPage = currentPage - 2;
      endPage = currentPage + 2;
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <Button
          key={i}
          variant={currentPage === i ? "default" : "outline"}
          size="sm"
          onClick={() => handlePageChange(i)}
          className="w-8 h-8 p-0"
        >
          {i}
        </Button>
      );
    }

    return pages;
  };

  return (
    <div className="space-y-6">
      {/* Nagłówek */}
      <div>
        <h1 className="text-3xl font-bold text-foreground flex items-center gap-2">
          Domeny
        </h1>
        <p className="text-muted-foreground mt-2">
          Przeglądaj i zarządzaj wszystkimi domenami w systemie
        </p>
      </div>

      {/* Domeny pogrupowane według CMS */}
      <Tabs
        value={activeCmsTab}
        onValueChange={handleTabChange}
        className="space-y-6"
      >
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="wordpress" className="flex items-center gap-2">
            <Code className="h-4 w-4" />
            {getCmsTabLabel("wordpress")}
          </TabsTrigger>
          <TabsTrigger value="inny" className="flex items-center gap-2">
            <Server className="h-4 w-4" />
            {getCmsTabLabel("inny")}
          </TabsTrigger>
          <TabsTrigger value="not-checked" className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            {getCmsTabLabel("not-checked")}
          </TabsTrigger>
        </TabsList>

        {["wordpress", "inny", "not-checked"].map((cmsType) => (
          <TabsContent key={cmsType} value={cmsType} className="space-y-6">
            {/* Filtry dla aktualnej zakładki */}
            {showFilters && (
              <Card>
                <CardHeader>
                  <CardTitle>Filtry - {getCmsTabLabel(cmsType)}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`search-${cmsType}`}>
                        Wyszukaj domenę
                      </Label>
                      <div className="relative">
                        <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id={`search-${cmsType}`}
                          placeholder="np. example.com"
                          value={
                            tabFilters[cmsType as keyof typeof tabFilters]
                              .searchTerm
                          }
                          onChange={(e) =>
                            updateTabFilter(
                              cmsType,
                              "searchTerm",
                              e.target.value
                            )
                          }
                          className="pl-10"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Kategoria</Label>
                      <Select
                        value={
                          tabFilters[cmsType as keyof typeof tabFilters]
                            .selectedCategory
                        }
                        onValueChange={(value) =>
                          updateTabFilter(cmsType, "selectedCategory", value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Wszystkie kategorie" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all-categories">
                            Wszystkie kategorie
                          </SelectItem>
                          {categories.map((category) => (
                            <SelectItem key={category} value={category}>
                              {category}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Status</Label>
                      <Select
                        value={
                          tabFilters[cmsType as keyof typeof tabFilters]
                            .selectedStatus
                        }
                        onValueChange={(value) =>
                          updateTabFilter(cmsType, "selectedStatus", value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Wszystkie statusy" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all-statuses">
                            Wszystkie statusy
                          </SelectItem>
                          <SelectItem value="new">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-gray-500"></div>
                              Nowa
                            </div>
                          </SelectItem>
                          <SelectItem value="in_progress">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                              W trakcie
                            </div>
                          </SelectItem>
                          <SelectItem value="accepted">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-green-500"></div>
                              Zaakceptowana
                            </div>
                          </SelectItem>
                          <SelectItem value="rejected">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-red-500"></div>
                              Odrzucona
                            </div>
                          </SelectItem>
                          <SelectItem value="closed">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-gray-400"></div>
                              Zamknięta
                            </div>
                          </SelectItem>
                          <SelectItem value="production">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                              Produkcyjna
                            </div>
                          </SelectItem>
                          <SelectItem value="hold">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                              Wstrzymana
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Ocena</Label>
                      <Select
                        value={
                          tabFilters[cmsType as keyof typeof tabFilters]
                            .selectedOcena
                        }
                        onValueChange={(value) =>
                          updateTabFilter(cmsType, "selectedOcena", value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Wszystkie oceny" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all-oceny">
                            Wszystkie oceny
                          </SelectItem>
                          <SelectItem value="wysoka">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-green-500"></div>
                              Wysoka
                            </div>
                          </SelectItem>
                          <SelectItem value="niska">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-red-500"></div>
                              Niska
                            </div>
                          </SelectItem>
                          <SelectItem value="brak">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-gray-400"></div>
                              Brak
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Sortuj według</Label>
                      <Select value={sortBy} onValueChange={setSortBy}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="domain">Nazwa domeny</SelectItem>
                          <SelectItem value="createdAt">
                            Data utworzenia
                          </SelectItem>
                          <SelectItem value="updatedAt">
                            Data aktualizacji
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Kolejność</Label>
                      <Select value={sortOrder} onValueChange={setSortOrder}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="desc">Malejąco</SelectItem>
                          <SelectItem value="asc">Rosnąco</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Lista domen dla aktualnej zakładki */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>
                    {getCmsTabLabel(cmsType)} ({pagination.totalCount})
                  </CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowFilters(!showFilters)}
                    className="flex items-center gap-2"
                  >
                    <Filter className="h-4 w-4" />
                    {showFilters ? "Ukryj filtry" : "Pokaż filtry"}
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {activeCmsTab === cmsType && (
                  <>
                    {isLoading ? (
                      <div className="text-center py-8">
                        <p className="text-muted-foreground">
                          Ładowanie domen...
                        </p>
                      </div>
                    ) : domains.length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-muted-foreground">
                          Brak domen spełniających kryteria wyszukiwania.
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {domains.map((domain) => (
                          <div
                            key={domain.id}
                            className={`group border rounded-lg p-4 hover:bg-muted/50 transition-colors ${
                              domain.status === "rejected" ? "opacity-40" : ""
                            }`}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                  <Globe className="h-4 w-4 text-muted-foreground" />
                                  <Link
                                    href={`/dashboard/domains/${domain.id}`}
                                    className="text-lg font-medium hover:text-primary transition-colors"
                                  >
                                    {domain.domain}
                                  </Link>
                                  {/* Phone icon for domains with contact metadata set to true */}
                                  {domain.metadata?.contact === true && (
                                    <span title="Dane kontaktowe dostępne">
                                      <Phone className="h-4 w-4 text-blue-600" />
                                    </span>
                                  )}
                                  <a
                                    href={domain.fullDomain}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-muted-foreground hover:text-primary"
                                  >
                                    <ExternalLink className="h-4 w-4" />
                                  </a>
                                </div>

                                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                  <Badge
                                    variant="secondary"
                                    className="text-xs"
                                  >
                                    {domain.category}
                                  </Badge>
                                  {domain.status === "rejected" && (
                                    <Badge
                                      variant="destructive"
                                      className="text-xs flex items-center gap-1"
                                    >
                                      <XCircle className="h-3 w-3" />
                                      Odrzucona
                                    </Badge>
                                  )}
                                  {domain.status === "accepted" && (
                                    <Badge
                                      variant="default"
                                      className="text-xs flex items-center gap-1 bg-green-600 hover:bg-green-600/80"
                                    >
                                      <CheckCircle className="h-3 w-3" />
                                      Zaakceptowana
                                    </Badge>
                                  )}
                                  {domain.status === "in_progress" && (
                                    <Badge
                                      variant="secondary"
                                      className="text-xs flex items-center gap-1 bg-blue-100 text-blue-800 hover:bg-blue-100/80"
                                    >
                                      <Clock className="h-3 w-3" />W trakcie
                                    </Badge>
                                  )}
                                  {domain.status === "closed" && (
                                    <Badge
                                      variant="outline"
                                      className="text-xs flex items-center gap-1"
                                    >
                                      <Ban className="h-3 w-3" />
                                      Zamknięta
                                    </Badge>
                                  )}
                                  {domain.status === "new" && (
                                    <Badge
                                      variant="secondary"
                                      className="text-xs flex items-center gap-1"
                                    >
                                      <AlertCircle className="h-3 w-3" />
                                      Nowa
                                    </Badge>
                                  )}
                                  {domain.status === "production" && (
                                    <Badge
                                      variant="default"
                                      className="text-xs flex items-center gap-1 bg-purple-600 hover:bg-purple-600/80"
                                    >
                                      <CheckCircle className="h-3 w-3" />
                                      Produkcyjna
                                    </Badge>
                                  )}
                                  {domain.status === "hold" && (
                                    <Badge
                                      variant="secondary"
                                      className="text-xs flex items-center gap-1 bg-orange-100 text-orange-800 hover:bg-orange-100/80"
                                    >
                                      <AlertCircle className="h-3 w-3" />
                                      Wstrzymana
                                    </Badge>
                                  )}
                                  {/* Ocena domeny */}
                                  {domain.ocena === "wysoka" && (
                                    <Badge
                                      variant="default"
                                      className="text-xs flex items-center gap-1 bg-green-600 hover:bg-green-600/80"
                                    >
                                      <CheckCircle className="h-3 w-3" />
                                      Wysoka ocena
                                    </Badge>
                                  )}
                                  {domain.ocena === "niska" && (
                                    <Badge
                                      variant="destructive"
                                      className="text-xs flex items-center gap-1"
                                    >
                                      <XCircle className="h-3 w-3" />
                                      Niska ocena
                                    </Badge>
                                  )}
                                  {(!domain.ocena || domain.ocena === "brak") && (
                                    <Badge
                                      variant="outline"
                                      className="text-xs flex items-center gap-1"
                                    >
                                      <AlertCircle className="h-3 w-3" />
                                      Brak oceny
                                    </Badge>
                                  )}
                                  <span className="flex items-center gap-1">
                                    <Calendar className="h-3 w-3" />
                                    {formatDate(domain.updatedAt)}
                                  </span>
                                </div>
                              </div>

                              <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                <Button asChild variant="outline" size="sm">
                                  <Link
                                    href={`/dashboard/domains/${domain.id}`}
                                  >
                                    Szczegóły
                                  </Link>
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Paginacja */}
                    {pagination.totalPages > 1 && (
                      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mt-6 border-t pt-4">
                        <div className="text-sm text-muted-foreground">
                          Strona {pagination.page} z {pagination.totalPages} (
                          {pagination.totalCount} domen)
                        </div>

                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              handlePageChange(pagination.page - 1)
                            }
                            disabled={!pagination.hasPrev}
                          >
                            <ChevronLeft className="h-4 w-4" />
                            Poprzednia
                          </Button>

                          {/* Numery stron */}
                          <div className="flex items-center gap-1">
                            {renderPaginationNumbers()}
                          </div>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              handlePageChange(pagination.page + 1)
                            }
                            disabled={!pagination.hasNext}
                          >
                            Następna
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}

export default function DomainsPage() {
  return (
    <Suspense
      fallback={
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-foreground flex items-center gap-2">
              Domeny
            </h1>
            <p className="text-muted-foreground mt-2">Ładowanie domen...</p>
          </div>
        </div>
      }
    >
      <DomainsPageContent />
    </Suspense>
  );
}
