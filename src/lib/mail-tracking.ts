import { sendMail as sendMailWithTracking } from 'nodemailer-mail-tracking';
import nodemailer from 'nodemailer';
import jwt from 'jsonwebtoken';
import EmailTracking from '@/models/EmailTracking';
import EmailHistory from '@/models/EmailHistory';
import connectDB from '@/lib/db';

interface TrackingData {
  recipient: string;
  timestamp?: string;
  emailHistoryId?: string | null;
  [key: string]: unknown;
}

interface MailTrackingEventData {
  recipient: string;
  link?: string;
  userAgent?: string;
  ipAddress?: string;
  [key: string]: unknown;
}

// Konfiguracja mail trackingu
export const getMailTrackingOptions = () => {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  const jwtSecret = process.env.NEXTAUTH_SECRET || 'fallback-secret';

  return {
    baseUrl: `${baseUrl}/api/mail-tracking`,
    jwtSecret,
    imageAlt: 'pixel image',
    getData: (data: TrackingData) => {
      // Dodaj dodatkowe dane do trackingu
      return {
        ...data,
        timestamp: new Date().toISOString(),
        source: 'ai-studio-audit'
      };
    },
    onBlankImageView: async (data: MailTrackingEventData) => {
      // Obsługa otwarcia emaila
      console.log('Email opened:', data);
      await handleEmailEvent('opened', data);
    },
    onLinkClick: async (data: MailTrackingEventData) => {
      // Obsługa kliknięcia w link
      console.log('Link clicked:', data);
      await handleEmailEvent('clicked', data);
    }
  };
};

// Obsługa eventów mailowych
async function handleEmailEvent(eventType: 'opened' | 'clicked', data: MailTrackingEventData) {
  try {
    await connectDB();

    // Znajdź EmailHistory na podstawie recipient
    const emailHistory = await EmailHistory.findOne({
      recipientEmail: data.recipient
    }).sort({ createdAt: -1 });

    if (!emailHistory) {
      console.error('Email history not found for recipient:', data.recipient);
      return;
    }

    // Dla eventów 'opened' sprawdź czy już nie został zapisany
    if (eventType === 'opened') {
      const existingOpenEvent = await EmailTracking.findOne({
        emailHistoryId: emailHistory._id,
        recipientEmail: data.recipient,
        eventType: 'opened'
      });

      if (existingOpenEvent) {
        console.log(`Email already opened by ${data.recipient}, skipping duplicate event`);
        return;
      }
    }

    // Utwórz nowy event trackingu
    const trackingEvent = new EmailTracking({
      emailHistoryId: emailHistory._id,
      domainId: emailHistory.domainId,
      recipientEmail: data.recipient,
      eventType,
      eventData: {
        link: data.link || undefined,
        userAgent: data.userAgent || undefined,
        ipAddress: data.ipAddress || undefined,
        timestamp: new Date(),
        source: data.source || 'unknown',
        ...data
      }
    });

    await trackingEvent.save();
    console.log(`Email ${eventType} event saved for ${data.recipient}`);

  } catch (error) {
    console.error(`Error handling email ${eventType} event:`, error);
  }
}

// Funkcja do wysyłania maili z trackingiem
export async function sendTrackedEmail(
  recipientEmail: string,
  subject: string,
  htmlContent: string,
  emailHistoryId?: string
) {
  try {
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: parseInt(process.env.SMTP_PORT || '587') === 465,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });

    const mailTrackingOptions = getMailTrackingOptions();

    // Dodaj emailHistoryId do danych trackingu jeśli jest dostępny
    const enhancedOptions = {
      ...mailTrackingOptions,
      getData: (data: TrackingData) => ({
        ...mailTrackingOptions.getData(data),
        emailHistoryId: emailHistoryId || null
      })
    };

    const sendMailOptions = {
      from: process.env.SMTP_FROM || process.env.SMTP_USER,
      to: recipientEmail,
      subject,
      html: htmlContent,
    };

    const results = await sendMailWithTracking(
      enhancedOptions,
      transporter,
      sendMailOptions
    );

    // Zapisz event 'sent' do trackingu
    if (emailHistoryId) {
      await connectDB();

      // Pobierz EmailHistory aby uzyskać domainId
      const emailHistory = await EmailHistory.findById(emailHistoryId);
      if (emailHistory) {
        const trackingEvent = new EmailTracking({
          emailHistoryId,
          domainId: emailHistory.domainId,
          recipientEmail,
          eventType: 'sent',
          eventData: {
            timestamp: new Date(),
            source: 'ai-studio-audit'
          }
        });
        await trackingEvent.save();
      }
    }

    return results;
  } catch (error) {
    console.error('Error sending tracked email:', error);
    throw error;
  }
}

// Funkcja pomocnicza do generowania JWT tokenu
export function generateTrackingToken(data: TrackingData) {
  const jwtSecret = process.env.NEXTAUTH_SECRET || 'fallback-secret';
  return jwt.sign(data, jwtSecret, { expiresIn: '1y' });
}
