// Skrypt do tworzenia unikalnego indeksu dla kontaktów (userId + contact_email)
// Uruchom: node scripts/create-contacts-unique-index.js

const { MongoClient } = require("mongodb");
const fs = require("fs");
const path = require("path");

// Funkcja do odczytania zmiennych środowiskowych z .env.local
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, "..", ".env.local");
    const envContent = fs.readFileSync(envPath, "utf8");
    const lines = envContent.split("\n");

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith("#")) {
        const [key, ...valueParts] = trimmedLine.split("=");
        if (key && valueParts.length > 0) {
          process.env[key.trim()] = valueParts.join("=").trim();
        }
      }
    }
  } catch (error) {
    console.error("Nie można odczytać pliku .env.local:", error.message);
  }
}

loadEnvFile();

async function createContactsUniqueIndex() {
  const client = new MongoClient(process.env.MONGODB_URI);

  try {
    await client.connect();
    console.log("Połączono z MongoDB");

    const db = client.db();
    const contactsCollection = db.collection("contacts");

    console.log("\n🔍 Sprawdzanie obecnych indeksów kontaktów...");
    const indexes = await contactsCollection.indexes();
    console.log("Obecne indeksy:", indexes.map(idx => ({ name: idx.name, key: idx.key, unique: idx.unique })));

    // Sprawdź czy unikalny indeks już istnieje
    const hasUniqueIndex = indexes.some(idx =>
      idx.name === "userId_1_contact_email_1" ||
      (idx.key && idx.key.userId === 1 && idx.key.contact_email === 1 && idx.unique)
    );

    if (hasUniqueIndex) {
      console.log("ℹ️  Unikalny indeks (userId + contact_email) już istnieje");
      return;
    }

    console.log("\n🔍 Sprawdzanie duplikatów kontaktów...");
    
    // Znajdź duplikaty kontaktów (ten sam email dla tego samego userId)
    const duplicates = await contactsCollection.aggregate([
      {
        $group: {
          _id: { contact_email: "$contact_email", userId: "$userId" },
          contacts: {
            $push: {
              id: "$_id",
              contact_companyName: "$contact_companyName",
              contact_contactPerson: "$contact_contactPerson",
              createdAt: "$createdAt"
            }
          },
          count: { $sum: 1 }
        }
      },
      {
        $match: { count: { $gt: 1 } }
      },
      {
        $sort: { count: -1 }
      }
    ]).toArray();

    console.log(`Znaleziono ${duplicates.length} grup duplikatów kontaktów`);

    if (duplicates.length > 0) {
      console.log("\n⚠️  UWAGA: Znaleziono duplikaty kontaktów!");
      console.log("Przed utworzeniem unikalnego indeksu należy usunąć duplikaty.");
      
      for (const duplicate of duplicates.slice(0, 5)) { // Pokaż tylko pierwsze 5
        console.log(`\n📧 Email: ${duplicate._id.contact_email} (userId: ${duplicate._id.userId})`);
        console.log(`   Liczba duplikatów: ${duplicate.count}`);
        duplicate.contacts.forEach((contact, index) => {
          console.log(`   ${index + 1}. ${contact.contact_companyName} - ${contact.contact_contactPerson} (${contact.createdAt})`);
        });
      }

      if (duplicates.length > 5) {
        console.log(`\n... i ${duplicates.length - 5} więcej grup duplikatów`);
      }

      console.log("\n❌ Nie można utworzyć unikalnego indeksu z powodu duplikatów.");
      console.log("Usuń duplikaty ręcznie lub napisz skrypt do ich usunięcia.");
      return;
    }

    console.log("\n✅ Brak duplikatów - można utworzyć unikalny indeks");

    console.log("\n🔧 Tworzenie unikalnego indeksu (userId + contact_email)...");
    
    // Utwórz unikalny indeks
    await contactsCollection.createIndex(
      { userId: 1, contact_email: 1 },
      {
        unique: true,
        name: "userId_1_contact_email_1",
        background: true,
      }
    );
    console.log("✅ Utworzono unikalny indeks userId_1_contact_email_1");

    console.log("\n🔍 Sprawdzanie nowych indeksów...");
    const newIndexes = await contactsCollection.indexes();
    console.log("Nowe indeksy:");
    newIndexes.forEach(idx => {
      console.log(`  - ${idx.name}: ${JSON.stringify(idx.key)} ${idx.unique ? '(UNIQUE)' : ''}`);
    });

    console.log("\n✅ Indeks unikalny dla kontaktów został utworzony pomyślnie!");
    console.log("Teraz API /api/contacts będzie automatycznie nadpisywać istniejące kontakty z tym samym emailem.");

  } catch (error) {
    console.error("❌ Błąd podczas tworzenia indeksu:", error);
    process.exit(1);
  } finally {
    await client.close();
    console.log("Rozłączono z MongoDB");
  }
}

createContactsUniqueIndex();
