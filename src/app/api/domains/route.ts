import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next'; // Changed import path
import { Session } from 'next-auth'; // Added Session import
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';
import Category from '@/models/Category';
import Log from '@/models/Log';
import { PipelineStage } from 'mongoose'; // Added PipelineStage import

interface DomainFilter {
  userId: string;
  category?: { $regex: RegExp };
  $and?: Array<Record<string, unknown>>;
  cms?: string | { $exists: boolean } | null;
  status?: string;
  ocena?: string | { $exists: boolean } | null;
}


export async function GET(request: NextRequest) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null; // Explicitly cast session

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const cms = searchParams.get('cms');
    const status = searchParams.get('status');
    const ocena = searchParams.get('ocena');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const sortBy = searchParams.get('sortBy') || 'createdAt'; // domain, createdAt
    const sortOrder = searchParams.get('sortOrder') || 'desc'; // asc, desc

    await connectDB();

    // Buduj filtr
    const filter: DomainFilter = { userId: session.user.id };

    if (category) {
      filter.category = { $regex: new RegExp(`^${category}$`, 'i') };
    }

    if (search) {
      filter.$and = filter.$and || [];
      filter.$and.push({
        $or: [
          { domain: { $regex: search, $options: 'i' } },
          { fullDomain: { $regex: search, $options: 'i' } }
        ]
      });
    }

    if (cms) {
      if (cms === 'not-checked') {
        // Domeny bez sprawdzonego CMS (pole cms nie istnieje lub jest null/undefined)
        filter.$and = filter.$and || [];
        filter.$and.push({
          $or: [
            { cms: { $exists: false } },
            { cms: null }
          ]
        });
      } else {
        // Konkretny typ CMS (wordpress, inny)
        filter.cms = cms;
      }
    }

    if (status) {
      filter.status = status;
    }

    if (ocena) {
      if (ocena === 'brak') {
        // Domeny bez oceny (pole ocena nie istnieje, jest null/undefined lub ma wartość 'brak')
        filter.$and = filter.$and || [];
        filter.$and.push({
          $or: [
            { ocena: { $exists: false } },
            { ocena: null },
            { ocena: 'brak' }
          ]
        });
      } else {
        // Konkretna ocena (wysoka, niska)
        filter.ocena = ocena;
      }
    }

    // Pobierz domeny z paginacją - używamy agregacji dla zaawansowanego sortowania
    const sortValue = sortOrder === 'asc' ? 1 : -1;
    const pipeline: PipelineStage[] = [ // Changed type to PipelineStage[]
      { $match: filter },
      {
        $addFields: {
          // Dodaj pole do sortowania: domeny 'new' mają priorytet 0, pozostałe 1
          statusPriority: {
            $cond: {
              if: { $eq: ["$status", "new"] },
              then: 0,
              else: 1
            }
          }
        }
      },
      {
        $sort: {
          // Najpierw sortuj po priorytecie statusu (new na początku)
          statusPriority: 1,
          // Następnie sortuj według wybranego kryterium
          [sortBy]: sortValue
        }
      },
      { $skip: (page - 1) * limit },
      { $limit: limit },
      {
        $project: {
          statusPriority: 0 // Usuń pole pomocnicze z wyników
        }
      }
    ];

    const domains = await Domain.aggregate(pipeline);

    // Policz całkowitą liczbę domen
    const totalCount = await Domain.countDocuments(filter);

    // Pobierz statystyki
    const stats = await Domain.aggregate([
      { $match: { userId: session.user.id } },
      {
        $group: {
          _id: '$category',
          totalDomains: { $sum: 1 },
          totalLinks: { $sum: '$linkCount' }
        }
      }
    ]);

    // Pobierz statystyki statusów domen
    const statusStats = await Domain.aggregate([
      { $match: { userId: session.user.id } },
      {
        $group: {
          _id: null,
          totalDomains: { $sum: 1 },
          verifiedDomains: {
            $sum: { $cond: [{ $eq: ['$status', 'accepted'] }, 1, 0] }
          },
          rejectedDomains: {
            $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] }
          },
          unprocessedDomains: {
            $sum: { $cond: [{ $eq: ['$status', 'new'] }, 1, 0] }
          },
          inProgressDomains: {
            $sum: { $cond: [{ $eq: ['$status', 'in_progress'] }, 1, 0] }
          },
          closedDomains: {
            $sum: { $cond: [{ $eq: ['$status', 'closed'] }, 1, 0] }
          },
          holdDomains: {
            $sum: { $cond: [{ $eq: ['$status', 'hold'] }, 1, 0] }
          }
        }
      }
    ]);

    // Pobierz kategorie z systemu kategorii
    const categoriesData = await Category.find({ userId: session.user.id })
      .sort({ name: 1 })
      .select('name');
    const categories = categoriesData.map(cat => cat.name);

    return NextResponse.json({
      success: true,
      domains: domains.map(domain => ({
        id: domain._id,
        domain: domain.domain,
        protocol: domain.protocol,
        fullDomain: domain.fullDomain,
        category: domain.category,
        linkCount: domain.linkCount,
        status: domain.status,
        cms: domain.cms,
        ocena: domain.ocena,
        metadata: domain.metadata || {},
        createdAt: domain.createdAt,
        updatedAt: domain.updatedAt
      })),
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page * limit < totalCount,
        hasPrev: page > 1
      },
      stats: stats.reduce((acc, stat) => {
        acc[stat._id] = {
          totalDomains: stat.totalDomains,
          totalLinks: stat.totalLinks
        };
        return acc;
      }, {} as Record<string, { totalDomains: number; totalLinks: number }>),
      statusStats: statusStats[0] || {
        totalDomains: 0,
        verifiedDomains: 0,
        rejectedDomains: 0,
        unprocessedDomains: 0,
        inProgressDomains: 0,
        closedDomains: 0,
        holdDomains: 0
      },
      categories
    });

  } catch (error) {
    console.error('Błąd pobierania domen:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas pobierania domen' },
      { status: 500 }
    );
  }
}

// DELETE endpoint do usuwania wszystkich domen użytkownika
export async function DELETE() { // Renamed request to _request
  try {
    const session = (await getServerSession(authOptions)) as Session | null; // Explicitly cast session

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    // Policz domeny i logi przed usunięciem
    const totalDomainsCount = await Domain.countDocuments({ userId: session.user.id });
    const userLogsCount = await Log.countDocuments({ userId: session.user.id });
    const systemLogsCount = await Log.countDocuments({ userId: "0" });
    const totalLogsCount = userLogsCount + systemLogsCount;

    if (totalDomainsCount === 0 && totalLogsCount === 0) {
      return NextResponse.json({
        success: true,
        message: 'Brak domen i logów do usunięcia',
        deletedDomainsCount: 0,
        deletedLogsCount: 0
      });
    }

    // Usuń wszystkie domeny użytkownika
    const domainsResult = await Domain.deleteMany({ userId: session.user.id });

    // Usuń wszystkie logi użytkownika i logi systemowe (userId: "0")
    const userLogsResult = await Log.deleteMany({ userId: session.user.id });
    const systemLogsResult = await Log.deleteMany({ userId: "0" });
    const totalDeletedLogs = userLogsResult.deletedCount + systemLogsResult.deletedCount;

    return NextResponse.json({
      success: true,
      message: `Usunięto ${domainsResult.deletedCount} domen i ${totalDeletedLogs} logów (${userLogsResult.deletedCount} użytkownika + ${systemLogsResult.deletedCount} systemowych)`,
      deletedDomainsCount: domainsResult.deletedCount,
      deletedLogsCount: totalDeletedLogs,
      deletedUserLogsCount: userLogsResult.deletedCount,
      deletedSystemLogsCount: systemLogsResult.deletedCount
    });

  } catch (error) {
    console.error('Błąd usuwania wszystkich domen:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas usuwania wszystkich domen' },
      { status: 500 }
    );
  }
}
