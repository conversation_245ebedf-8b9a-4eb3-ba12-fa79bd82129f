import mongoose, { Document, Schema } from 'mongoose';

export interface IEmailTracking extends Document {
  emailHistoryId: string; // Referencja do EmailHistory
  domainId: string;
  recipientEmail: string;
  eventType: 'sent' | 'delivered' | 'opened' | 'clicked' | 'bounced' | 'complained';
  eventData?: {
    link?: string; // Dla eventów 'clicked'
    userAgent?: string;
    ipAddress?: string;
    timestamp?: Date;
    [key: string]: string | number | boolean | Date | null | undefined;
  };
  createdAt: Date;
  updatedAt: Date;
}

const EmailTrackingSchema: Schema = new Schema({
  emailHistoryId: {
    type: String,
    required: [true, 'ID historii emaila jest wymagane'],
    ref: 'EmailHistory'
  },
  domainId: {
    type: String,
    required: [true, 'ID domeny jest wymagane'],
    ref: 'Domain'
  },
  recipientEmail: {
    type: String,
    required: [true, 'Email odbiorcy jest wymagany'],
    trim: true,
    lowercase: true,
    maxlength: [255, '<PERSON>ail odbiorcy nie może być dłuższy niż 255 znaków']
  },
  eventType: {
    type: String,
    required: [true, 'Typ eventu jest wymagany'],
    enum: {
      values: ['sent', 'delivered', 'opened', 'clicked', 'bounced', 'complained'],
      message: 'Typ eventu musi być jednym z: sent, delivered, opened, clicked, bounced, complained'
    }
  },
  eventData: {
    link: { type: String },
    userAgent: { type: String },
    ipAddress: { type: String },
    timestamp: { type: Date, default: Date.now },
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Indeksy dla szybszego wyszukiwania
EmailTrackingSchema.index({ emailHistoryId: 1, eventType: 1 });
EmailTrackingSchema.index({ domainId: 1, eventType: 1, createdAt: -1 });
EmailTrackingSchema.index({ recipientEmail: 1, eventType: 1, createdAt: -1 });
EmailTrackingSchema.index({ eventType: 1, createdAt: -1 });

export default mongoose.models.EmailTracking || mongoose.model<IEmailTracking>('EmailTracking', EmailTrackingSchema);
