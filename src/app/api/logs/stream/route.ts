// src/app/api/logs/stream/route.ts
import { NextRequest } from 'next/server';
import connectDB from '@/lib/db';
import Log, { ILog } from '@/models/Log'; // Assuming ILog is exported from Log.ts

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const stream = new ReadableStream({
      async start(controller) {
        const changeStream = Log.watch();

        changeStream.on('change', (change) => {
          if (change.operationType === 'insert') {
            const newLog: ILog = change.fullDocument as ILog; // Type newLog as ILog
            controller.enqueue(`data: ${JSON.stringify(newLog)}\n\n`);
          }
        });

        changeStream.on('error', (error) => {
          console.error('Change Stream error:', error);
          controller.error(error); // Close the stream with an error
          changeStream.close();
        });

        request.signal.onabort = () => {
          console.log('Client disconnected, closing change stream.');
          changeStream.close();
          controller.close();
        };

        // Send a keep-alive message every 20 seconds to prevent timeouts
        const keepAliveInterval = setInterval(() => {
          controller.enqueue(': keep-alive\n\n');
        }, 20000);

        request.signal.addEventListener('abort', () => {
          clearInterval(keepAliveInterval);
        });

      },
      cancel() {
        // This cancel function is called if the consumer calls reader.cancel()
        // or if the stream is piped to a WritableStream that aborts.
        // We already handle abort via request.signal.onabort for client disconnects.
        console.log('SSE stream cancelled.');
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*', // Adjust for production
      },
    });

  } catch (error) {
    console.error('Error setting up SSE stream:', error);
    return new Response(JSON.stringify({ error: 'Failed to set up SSE stream' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
}

// Add a keep-alive mechanism by sending a comment line periodically
// This is handled within the ReadableStream now.

// Ensure that the Log model is correctly imported and used.
// Ensure ILog interface is available if change.fullDocument is typed.
// It's good practice to ensure ILog is exported from your models/Log.ts
// export interface ILog extends Document { ... }
