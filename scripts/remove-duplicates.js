// Skrypt do usuwania duplikatów domen z bazy danych
// Uruchom: node scripts/remove-duplicates.js [strategy] [dryRun]
// Strategie: keep-oldest, keep-newest, keep-highest-links
// dryRun: true/false (domyś<PERSON>ie true)

const { MongoClient, ObjectId } = require("mongodb");
const fs = require("fs");
const path = require("path");

// Funkcja do odczytania zmiennych środowiskowych z .env
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, "..", ".env");
    const envContent = fs.readFileSync(envPath, "utf8");
    const lines = envContent.split("\n");

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith("#")) {
        const [key, ...valueParts] = trimmedLine.split("=");
        if (key && valueParts.length > 0) {
          process.env[key.trim()] = valueParts.join("=").trim();
        }
      }
    }
  } catch (error) {
    console.error("Nie można odczytać pliku .env:", error.message);
  }
}

loadEnvFile();

async function removeDuplicates(strategy = 'keep-highest-links', dryRun = true) {
  const client = new MongoClient(process.env.MONGODB_URI);

  try {
    await client.connect();
    console.log("Połączono z MongoDB");

    const db = client.db();
    const domainsCollection = db.collection("domains");

    console.log(`\n🔧 Strategia: ${strategy}`);
    console.log(`🧪 Tryb testowy: ${dryRun ? 'TAK' : 'NIE'}`);

    if (!['keep-oldest', 'keep-newest', 'keep-highest-links'].includes(strategy)) {
      throw new Error('Nieprawidłowa strategia. Dostępne: keep-oldest, keep-newest, keep-highest-links');
    }

    console.log("\n🔍 Znajdowanie duplikatów...");
    
    // Znajdź duplikaty domen
    const duplicates = await domainsCollection.aggregate([
      {
        $group: {
          _id: { fullDomain: "$fullDomain", userId: "$userId" },
          domains: {
            $push: {
              id: "$_id",
              category: "$category",
              status: "$status",
              linkCount: "$linkCount",
              createdAt: "$createdAt",
              updatedAt: "$updatedAt"
            }
          },
          count: { $sum: 1 }
        }
      },
      {
        $match: { count: { $gt: 1 } }
      },
      {
        $sort: { count: -1 }
      }
    ]).toArray();

    console.log(`Znaleziono ${duplicates.length} grup duplikatów`);

    if (duplicates.length === 0) {
      console.log("✅ Brak duplikatów do usunięcia");
      return;
    }

    const toDelete = [];
    const toKeep = [];
    const decisions = [];

    console.log("\n📋 Analizowanie duplikatów...");

    duplicates.forEach((group, index) => {
      const domains = group.domains;
      let domainToKeep;

      switch (strategy) {
        case 'keep-oldest':
          domainToKeep = domains.reduce((oldest, current) => 
            new Date(current.createdAt) < new Date(oldest.createdAt) ? current : oldest
          );
          break;
        case 'keep-newest':
          domainToKeep = domains.reduce((newest, current) => 
            new Date(current.createdAt) > new Date(newest.createdAt) ? current : newest
          );
          break;
        case 'keep-highest-links':
          domainToKeep = domains.reduce((highest, current) => 
            current.linkCount > highest.linkCount ? current : highest
          );
          break;
      }

      toKeep.push(domainToKeep.id);
      
      const domainsToDelete = domains.filter(domain => domain.id !== domainToKeep.id);
      toDelete.push(...domainsToDelete.map(d => d.id));

      decisions.push({
        domain: group._id.fullDomain,
        totalCount: group.count,
        kept: {
          id: domainToKeep.id,
          category: domainToKeep.category,
          status: domainToKeep.status,
          linkCount: domainToKeep.linkCount,
          createdAt: domainToKeep.createdAt
        },
        deleted: domainsToDelete.map(d => ({
          id: d.id,
          category: d.category,
          status: d.status,
          linkCount: d.linkCount,
          createdAt: d.createdAt
        }))
      });

      if (index < 10) { // Pokaż pierwsze 10 decyzji
        console.log(`${index + 1}. ${group._id.fullDomain}:`);
        console.log(`   Zachowuję: ${domainToKeep.category} (${domainToKeep.linkCount} linków, ${domainToKeep.status})`);
        console.log(`   Usuwam: ${domainsToDelete.map(d => `${d.category} (${d.linkCount} linków)`).join(', ')}`);
      }
    });

    console.log(`\n📊 Podsumowanie:`);
    console.log(`- Grup duplikatów: ${duplicates.length}`);
    console.log(`- Domen do zachowania: ${toKeep.length}`);
    console.log(`- Domen do usunięcia: ${toDelete.length}`);

    // Znajdź szczegóły dla panoramafirm.pl
    const panoramaDecision = decisions.find(d => d.domain.includes('panoramafirm.pl'));
    if (panoramaDecision) {
      console.log(`\n🎯 Decyzja dla panoramafirm.pl:`);
      console.log(`Zachowuję: ${panoramaDecision.kept.category} (${panoramaDecision.kept.linkCount} linków, ${panoramaDecision.kept.status})`);
      console.log(`Usuwam:`);
      panoramaDecision.deleted.forEach((d, i) => {
        console.log(`  ${i + 1}. ${d.category} (${d.linkCount} linków, ${d.status})`);
      });
    }

    if (!dryRun && toDelete.length > 0) {
      console.log(`\n🗑️  Usuwanie ${toDelete.length} duplikatów...`);
      
      const result = await domainsCollection.deleteMany({
        _id: { $in: toDelete.map(id => new ObjectId(id)) }
      });
      
      console.log(`✅ Usunięto ${result.deletedCount} domen`);
      
      if (result.deletedCount !== toDelete.length) {
        console.log(`⚠️  Ostrzeżenie: Oczekiwano usunięcia ${toDelete.length}, ale usunięto ${result.deletedCount}`);
      }
    } else if (dryRun) {
      console.log(`\n🧪 Tryb testowy - żadne domeny nie zostały usunięte`);
      console.log(`Aby faktycznie usunąć duplikaty, uruchom:`);
      console.log(`node scripts/remove-duplicates.js ${strategy} false`);
    }

  } catch (error) {
    console.error("❌ Błąd podczas usuwania duplikatów:", error);
  } finally {
    await client.close();
    console.log("\nRozłączono z MongoDB");
  }
}

// Sprawdź czy skrypt jest uruchamiany bezpośrednio
if (require.main === module) {
  const args = process.argv.slice(2);
  const strategy = args[0] || 'keep-highest-links';
  const dryRun = args[1] !== 'false'; // Domyślnie true, false tylko gdy explicit "false"
  
  console.log("🗑️  Usuwanie duplikatów domen...");
  console.log(`Strategia: ${strategy}`);
  console.log(`Tryb testowy: ${dryRun}`);
  
  removeDuplicates(strategy, dryRun).catch(error => {
    console.error("Błąd:", error);
    process.exit(1);
  });
}

module.exports = { removeDuplicates };
