import mongoose, { Document, Schema } from 'mongoose';

export interface IDomain extends Document {
  domain: string;
  protocol: string;
  fullDomain: string; // protocol + domain
  category: string;
  userId: string;
  linkCount: number; // liczba linków z tej domeny
  status: 'new' | 'accepted' | 'rejected' | 'closed' | 'in_progress' | 'in_audit' | 'production' | 'hold'; // status domeny
  cms?: string; // typ CMS: 'wordpress', 'inny'
  ocena?: 'wysoka' | 'niska' | 'brak'; // ocena domeny: wysoka, niska, brak
  auditContent?: string; // treść audytu domeny
  lighthouseContent?: string; // wyniki audytu Lighthouse
  contact_data?: {
    contact_companyName?: string;
    contact_contactPerson?: string;
    contact_email?: string;
    contact_phone?: string;
    contact_address?: string;
    contact_notes?: string;
    contact_lastUpdated?: Date;
  };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  metadata?: Record<string, any>; // pole mix na dowolne informacje o domenie
  createdAt: Date;
  updatedAt: Date;
}

const DomainSchema: Schema = new Schema({
  domain: {
    type: String,
    required: [true, 'Domena jest wymagana'],
    trim: true,
    lowercase: true,
    maxlength: [255, 'Domena nie może być dłuższa niż 255 znaków']
  },
  protocol: {
    type: String,
    required: [true, 'Protokół jest wymagany'],
    enum: ['http', 'https'],
    default: 'https'
  },
  fullDomain: {
    type: String,
    required: [true, 'Pełna domena jest wymagana'],
    trim: true,
    lowercase: true
  },
  category: {
    type: String,
    required: [true, 'Kategoria jest wymagana'],
    trim: true,
    maxlength: [100, 'Kategoria nie może być dłuższa niż 100 znaków']
  },
  userId: {
    type: String,
    required: [true, 'ID użytkownika jest wymagane']
  },
  linkCount: {
    type: Number,
    default: 1,
    min: [1, 'Liczba linków musi być większa niż 0']
  },
  status: {
    type: String,
    enum: ['new', 'accepted', 'rejected', 'closed', 'in_progress', 'in_audit', 'production', 'hold'],
    default: 'new'
  },
  cms: {
    type: String,
    enum: ['wordpress', 'inny'],
    required: false
  },
  ocena: {
    type: String,
    enum: ['wysoka', 'niska', 'brak'],
    required: false
  },
  auditContent: {
    type: String,
    required: false
  },
  lighthouseContent: {
    type: String,
    required: false
  },
  contact_data: {
    contact_companyName: { type: String, default: '' },
    contact_contactPerson: { type: String, default: '' },
    contact_email: { type: String, default: '' },
    contact_phone: { type: String, default: '' },
    contact_address: { type: String, default: '' },
    contact_notes: { type: String, default: '' },
    contact_lastUpdated: { type: Date }
  },
  metadata: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Indeks złożony dla globalnej unikalności domeny dla użytkownika (bez kategorii)
DomainSchema.index({ userId: 1, fullDomain: 1 }, { unique: true });

// Indeks dla szybszego wyszukiwania domen użytkownika po kategorii
DomainSchema.index({ userId: 1, category: 1 });

// Indeks dla wyszukiwania po statusie domeny dla użytkownika
DomainSchema.index({ userId: 1, status: 1 });

// Indeks dla wyszukiwania po CMS dla użytkownika
DomainSchema.index({ userId: 1, cms: 1 });

// Indeks dla wyszukiwania po domenie (globalny)
DomainSchema.index({ domain: 1 });

// Indeks dla sortowania po dacie utworzenia
DomainSchema.index({ createdAt: 1 });

export default mongoose.models.Domain || mongoose.model<IDomain>('Domain', DomainSchema);
