import { NextResponse } from 'next/server'; // Removed NextRequest
import { getServerSession } from 'next-auth/next'; // Changed import path
import { Session } from 'next-auth'; // Added Session import
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';

export async function GET() {
  try {
    const session = (await getServerSession(authOptions)) as Session | null; // Explicitly cast session

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    // Pobierz wszystkie domeny ze statusem "new" i CMS ustawionym na WordPress
    const domains = await Domain.find({
      userId: session.user.id,
      status: 'new',
      cms: 'wordpress'
    }).select('_id domain fullDomain category linkCount cms createdAt');

    // Przygotuj dane do CSV
    const csvData = domains.map(domain => ({
      id: domain._id.toString(),
      domain: domain.domain,
      fullDomain: domain.fullDomain,
      category: domain.category,
      linkCount: domain.linkCount,
      cms: domain.cms || '',
      createdAt: domain.createdAt.toISOString()
    }));

    return NextResponse.json({
      success: true,
      data: csvData,
      count: csvData.length
    });

  } catch (error) {
    console.error('Błąd eksportu domen:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas eksportu domen' },
      { status: 500 }
    );
  }
}
