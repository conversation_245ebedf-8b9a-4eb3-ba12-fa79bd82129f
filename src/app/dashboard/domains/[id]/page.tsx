"use client";

import { useState, useEffect, useCallback } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { <PERSON>roll<PERSON><PERSON> } from "@/components/ui/scroll-area";
import { toast } from "sonner";
import {
  ArrowLeft,
  ExternalLink,
  Calendar,
  Database,
  CheckCircle,
  Code,
  Server,
  Building,
  User,
  Mail,
  Phone,
  MapPin,
  FileText,
  Edit,
  Globe,
  Activity,
  Eye,
  ShieldCheck,
  Trash2,
} from "lucide-react";

import MetadataManager from "@/components/domain/MetadataManager";
import EmailHistory from "@/components/domain/EmailHistory";
import MailTrackingStats from "@/components/domain/MailTrackingStats";
import { RelatedDomainsSidebar } from "@/components/domain/RelatedDomainsSidebar";

interface ContactData {
  contact_companyName?: string;
  contact_contactPerson?: string;
  contact_email?: string;
  contact_phone?: string;
  contact_address?: string;
  contact_notes?: string;
  contact_lastUpdated?: string;
}

interface Domain {
  id: string;
  domain: string;
  protocol: string;
  fullDomain: string;
  category: string;
  linkCount: number;
  status: "new" | "accepted" | "rejected" | "closed" | "in_progress" | "in_audit" | "production" | "hold";
  cms?: string;
  ocena?: "wysoka" | "niska" | "brak";
  auditContent?: string;
  lighthouseContent?: string;
  secureAudit: boolean;
  contact_data?: ContactData;
  metadata?: Record<string, string | number | boolean | null>;
  createdAt: string;
  updatedAt: string;
}

interface DomainDetailsResponse {
  success: boolean;
  domain: Domain;
}

interface Category {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export default function DomainDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [domain, setDomain] = useState<Domain | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSavingDomainInfo, setIsSavingDomainInfo] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [lighthouseContent, setLighthouseContent] = useState<string>("");

  const [iframeError, setIframeError] = useState(false);
  const [showLighthouseModal, setShowLighthouseModal] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Domain info form state
  const [domainInfoData, setDomainInfoData] = useState({
    cms: "",
    status: "",
    category: "",
    ocena: "",
  });

  // Contact form state
  const [isSavingContact, setIsSavingContact] = useState(false);
  const [contactData, setContactData] = useState({
    companyName: "",
    contactPerson: "",
    email: "",
    phone: "",
    address: "",
    notes: "",
  });
  const [isExecutingAudit, setIsExecutingAudit] = useState(false);

  const fetchDomainDetails = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/domains/${params.id}`);

      if (!response.ok) {
        if (response.status === 404) {
          toast.error("Domena nie została znaleziona");
          router.push("/dashboard/domains");
          return;
        }
        throw new Error("Błąd pobierania szczegółów domeny");
      }

      const data: DomainDetailsResponse = await response.json();
      setDomain(data.domain);

      // Fetch lighthouse content separately if needed
      const auditResponse = await fetch(`/api/domains/${params.id}/audit-content`);
      if (auditResponse.ok) {
        const auditData = await auditResponse.json();
        if (auditData.success) {
          setLighthouseContent(auditData.lighthouseContent || "");
        }
      }
    } catch (error) {
      console.error("Błąd pobierania szczegółów domeny:", error);
      toast.error("Wystąpił błąd podczas pobierania szczegółów domeny");
    } finally {
      setIsLoading(false);
    }
  }, [params.id, router]);

  const fetchCategories = useCallback(async () => {
    try {
      const response = await fetch("/api/categories");
      if (!response.ok) {
        throw new Error("Błąd pobierania kategorii");
      }
      const data = await response.json();
      setCategories(data.categories);
    } catch (error) {
      console.error("Błąd pobierania kategorii:", error);
      toast.error("Wystąpił błąd podczas pobierania kategorii");
    }
  }, []);

  // Save domain info function
  const saveDomainInfo = async () => {
    if (!domain) return;

    setIsSavingDomainInfo(true);
    try {
      const response = await fetch(`/api/domains/${params.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cms: domainInfoData.cms === "none" ? null : domainInfoData.cms,
          status: domainInfoData.status,
          category: domainInfoData.category,
          ocena: domainInfoData.ocena === "brak" ? null : domainInfoData.ocena,
        }),
      });

      if (!response.ok) {
        throw new Error("Błąd aktualizacji informacji o domenie");
      }

      const data = await response.json();
      setDomain(data.domain);
      toast.success("Informacje o domenie zostały zapisane");
    } catch (error) {
      console.error("Błąd aktualizacji informacji o domenie:", error);
      toast.error("Wystąpił błąd podczas aktualizacji informacji o domenie");
    } finally {
      setIsSavingDomainInfo(false);
    }
  };

  // Handle domain info changes
  const handleDomainInfoChange = (
    field: keyof typeof domainInfoData,
    value: string
  ) => {
    setDomainInfoData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Contact form functions
  const fetchContactData = useCallback(async () => {
    if (!domain) return;

    try {
      const response = await fetch(`/api/domains/${domain.id}/contact`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.contact) {
          setContactData(data.contact);
        }
      }
    } catch (error) {
      console.error("Błąd pobierania danych kontaktowych:", error);
    }
  }, [domain]);

  const saveContactData = async (data: typeof contactData) => {
    if (!domain) return;

    setIsSavingContact(true);
    try {
      const response = await fetch(`/api/domains/${domain.id}/contact`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error("Błąd zapisywania danych kontaktowych");
      }

      const result = await response.json();
      if (result.success) {
        toast.success("Dane kontaktowe zostały zapisane");
        // Nie odświeżamy danych domeny - dane kontaktowe są przechowywane osobno
      }
    } catch (error) {
      console.error("Błąd zapisywania danych kontaktowych:", error);
      toast.error("Nie udało się zapisać danych kontaktowych");
    } finally {
      setIsSavingContact(false);
    }
  };

  const handleContactChange = (
    field: keyof typeof contactData,
    value: string
  ) => {
    setContactData({ ...contactData, [field]: value });
  };

  useEffect(() => {
    if (params.id) {
      fetchDomainDetails();
    }
    fetchCategories();
  }, [params.id, fetchDomainDetails, fetchCategories]);

  // Load contact data when domain is loaded - only when domain.id changes, not the entire domain object
  useEffect(() => {
    if (domain?.id) {
      fetchContactData();
      // Initialize domain info form data
      setDomainInfoData({
        cms: domain.cms || "none",
        status: domain.status,
        category: domain.category,
        ocena: domain.ocena || "brak",
      });
    }
  }, [domain?.id, domain?.cms, domain?.status, domain?.category, domain?.ocena, fetchContactData]);



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pl-PL", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Helper functions for iframe
  const handleIframeError = () => {
    setIframeError(true);
  };

  const openInNewTab = () => {
    if (domain) {
      window.open(domain.fullDomain, "_blank", "noopener,noreferrer");
    }
  };

  const toggleIframeError = () => {
    setIframeError(!iframeError);
  };

  // Execute audit
  const executeAudit = async () => {
    if (!params.id || !domain) return;

    setIsExecutingAudit(true);
    try {
      const response = await fetch(`/api/domains/${params.id}/execute-audit`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Błąd wykonywania audytu");
      }

      const data = await response.json();
      if (data.success) {
        toast.success("Żądanie audytu zostało wysłane pomyślnie");
        // Odśwież dane domeny
        await fetchDomainDetails();
      }
    } catch (error) {
      console.error("Błąd wykonywania audytu:", error);
      toast.error(error instanceof Error ? error.message : "Wystąpił błąd podczas wykonywania audytu");
    } finally {
      setIsExecutingAudit(false);
    }
  };

  // Delete domain function
  const handleDeleteDomain = async () => {
    if (!params.id || !domain) return;

    setIsDeleting(true);
    try {
      const response = await fetch(`/api/domains/${params.id}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Błąd usuwania domeny");
      }

      const data = await response.json();
      if (data.success) {
        toast.success(data.message);
        // Przekieruj do listy domen
        router.push("/dashboard/domains");
      }
    } catch (error) {
      console.error("Błąd usuwania domeny:", error);
      toast.error(error instanceof Error ? error.message : "Wystąpił błąd podczas usuwania domeny");
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">
          <p className="text-muted-foreground">
            Ładowanie szczegółów domeny...
          </p>
        </div>
      </div>
    );
  }

  if (!domain) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">
          <p className="text-muted-foreground">
            Domena nie została znaleziona.
          </p>
          <Button asChild className="mt-4">
            <Link href="/dashboard/domains">Powrót do listy domen</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 xl:grid-cols-[1fr_350px] gap-6">
      {/* Main content */}
      <div className="space-y-6">
        {/* Domain Preview and Basic Info */}
        <div className="grid grid-cols-1 lg:grid-cols-[55%_1fr] gap-6">
        {/* Iframe preview */}
        <Card className="pb-0">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/dashboard/domains">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Powrót
                  </Link>
                </Button>
                <Link
                  href={domain.fullDomain}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {domain.domain}
                </Link>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  onClick={toggleIframeError}
                  variant="ghost"
                  size="sm"
                  className="text-xs"
                >
                  {iframeError ? "Pokaż iframe" : "Ukryj iframe"}
                </Button>
                <Button
                  onClick={openInNewTab}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <ExternalLink className="h-4 w-4" />
                  Otwórz w nowej karcie
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            {iframeError ? (
              <div className="p-6 text-center">
                <div className="text-muted-foreground mb-4">
                  <Globe className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>Nie można wyświetlić podglądu domeny</p>
                  <p className="text-sm">
                    Domena może blokować wyświetlanie w ramce
                  </p>
                </div>
                <Button onClick={openInNewTab} variant="outline">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Otwórz {domain.domain} w nowej karcie
                </Button>
              </div>
            ) : (
              <iframe
                src={domain.fullDomain}
                className="w-full h-[600px] border-0 rounded-b-lg overflow-x-hidden"
                title={`Podgląd domeny ${domain.domain}`}
                sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-top-navigation"
                onError={handleIframeError}
              />
            )}
          </CardContent>
        </Card>

        {/* Informacje podstawowe */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Informacje o domenie
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowLighthouseModal(true)}
                  className="flex items-center gap-2"
                  disabled={!lighthouseContent}
                >
                  <Activity className="h-4 w-4" />
                  Lighthouse
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  asChild
                  className="flex items-center gap-2"
                >
                  <Link href={`/dashboard/domains/${domain.id}/audit`}>
                    <FileText className="h-4 w-4" />
                    Audyt
                  </Link>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  asChild
                  className="flex items-center gap-2"
                >
                  <Link
                    href={`https://audyt-bezpczenstwa.pl/audyty/${domain.id}.html`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Eye className="h-4 w-4" />
                    Podgląd Lighthouse
                  </Link>
                </Button>
                <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="destructive"
                      size="sm"
                      className="flex items-center gap-2"
                    >
                      <Trash2 className="h-4 w-4" />
                      Usuń domenę
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Usuń domenę</AlertDialogTitle>
                      <AlertDialogDescription>
                        Czy na pewno chcesz usunąć domenę <strong>{domain.domain}</strong>?
                        <br />
                        <br />
                        Ta akcja spowoduje również usunięcie wszystkich powiązanych linków i jest nieodwracalna.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel disabled={isDeleting}>
                        Anuluj
                      </AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handleDeleteDomain}
                        disabled={isDeleting}
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                      >
                        {isDeleting ? "Usuwanie..." : "Usuń domenę"}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <p className="text-sm text-muted-foreground">Pełny adres</p>
                <p className="font-medium">{domain.fullDomain}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Kategoria</p>
                <Select
                  value={domainInfoData.category}
                  onValueChange={(value) =>
                    handleDomainInfoChange("category", value)
                  }
                  disabled={isSavingDomainInfo}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.length === 0 ? (
                      <SelectItem value="no-categories" disabled>
                        Brak kategorii. Dodaj kategorie w ustawieniach.
                      </SelectItem>
                    ) : (
                      categories.map((category) => (
                        <SelectItem key={category.id} value={category.name}>
                          {category.name}
                          {category.description && (
                            <span className="text-muted-foreground ml-2">
                              - {category.description}
                            </span>
                          )}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">CMS</p>
                <Select
                  value={domainInfoData.cms}
                  onValueChange={(value) =>
                    handleDomainInfoChange("cms", value)
                  }
                  disabled={isSavingDomainInfo}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">
                      <span className="text-muted-foreground">
                        Nie sprawdzono
                      </span>
                    </SelectItem>
                    <SelectItem value="wordpress">
                      <div className="flex items-center gap-2">
                        <Code className="h-4 w-4 text-blue-600" />
                        <span className="text-blue-600">WordPress</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="inny">
                      <div className="flex items-center gap-2">
                        <Server className="h-4 w-4 text-gray-600" />
                        <span className="text-gray-600">Inny CMS</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Data utworzenia</p>
                <p className="font-medium flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  {formatDate(domain.createdAt)}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">
                  Ostatnia aktualizacja
                </p>
                <p className="font-medium flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  {formatDate(domain.updatedAt)}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Status</p>
                <div className="mt-1">
                  <Select
                    value={domainInfoData.status}
                    onValueChange={(value) =>
                      handleDomainInfoChange("status", value)
                    }
                    disabled={isSavingDomainInfo}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="new">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-gray-500"></div>
                          Nowa
                        </div>
                      </SelectItem>
<SelectItem value="in_audit">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                          Trwa audyt
                        </div>
                      </SelectItem>
                      <SelectItem value="in_progress">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                          W trakcie
                        </div>
                      </SelectItem>
                      <SelectItem value="accepted">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-green-500"></div>
                          Zaakceptowana
                        </div>
                      </SelectItem>
                      <SelectItem value="rejected">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-red-500"></div>
                          Odrzucona
                        </div>
                      </SelectItem>
                      <SelectItem value="closed">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-gray-400"></div>
                          Zamknięta
                        </div>
                      </SelectItem>
                      <SelectItem value="production">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                          Produkcyjna
                        </div>
                      </SelectItem>
                      <SelectItem value="hold">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                          Wstrzymana
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Ocena ryzyka ataku</p>
                <Select
                  value={domainInfoData.ocena}
                  onValueChange={(value) =>
                    handleDomainInfoChange("ocena", value)
                  }
                  disabled={isSavingDomainInfo}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="brak">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-gray-400"></div>
                        Brak
                      </div>
                    </SelectItem>
                    <SelectItem value="niska">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-red-500"></div>
                        Niska
                      </div>
                    </SelectItem>
                    <SelectItem value="wysoka">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-green-500"></div>
                        Wysoka
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <p className="text-sm text-muted-foreground">
                  Audyt bezpieczeństwa
                </p>
                {domain.secureAudit ? (
                  <div className="flex items-center gap-2 mt-1">
                    <div className="flex items-center gap-1 text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 px-2 py-1 rounded-md w-fit">
                      <ShieldCheck className="h-3 w-3" />
                      Audyt OK
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-900/20 dark:text-orange-300 dark:border-orange-800">
                      Nie przeprowadzono
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={executeAudit}
                      disabled={isExecutingAudit || domain.status === 'in_audit'}
                      className="text-xs h-7 px-3 hover:bg-primary hover:text-primary-foreground transition-colors"
                    >
                      {isExecutingAudit ? (
                        <>
                          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-primary mr-1"></div>
                          Wykonywanie...
                        </>
                      ) : domain.status === 'in_audit' ? (
                        <>
                          <div className="animate-pulse h-3 w-3 rounded-full bg-blue-500 mr-1"></div>
                          W trakcie audytu
                        </>
                      ) : (
                        <>
                          <ShieldCheck className="h-3 w-3 mr-1" />
                          Wykonaj audyt
                        </>
                      )}
                    </Button>
                  </div>
                )}
              </div>

              {/* Save button */}
              <div className="col-span-full flex justify-end pt-4">
                <Button
                  onClick={saveDomainInfo}
                  disabled={isSavingDomainInfo}
                  className="flex items-center gap-2"
                >
                  {isSavingDomainInfo ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Zapisywanie...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4" />
                      Zapisz informacje o domenie
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Contact data and Metadata in one row on desktop */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Formularz edycji danych kontaktowych */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Edit className="h-5 w-5" />
                Edytuj dane kontaktowe
              </div>
              {isSavingContact && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                  Zapisywanie...
                </div>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* First row: Company Name and Contact Person */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label
                    htmlFor="companyName"
                    className="flex items-center gap-2"
                  >
                    <Building className="h-4 w-4" />
                    Nazwa firmy
                  </Label>
                  <Input
                    id="companyName"
                    value={contactData.companyName}
                    onChange={(e) =>
                      handleContactChange("companyName", e.target.value)
                    }
                    placeholder="Wprowadź nazwę firmy"
                  />
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="contactPerson"
                    className="flex items-center gap-2"
                  >
                    <User className="h-4 w-4" />
                    Osoba kontaktowa
                  </Label>
                  <Input
                    id="contactPerson"
                    value={contactData.contactPerson}
                    onChange={(e) =>
                      handleContactChange("contactPerson", e.target.value)
                    }
                    placeholder="Imię i nazwisko"
                  />
                </div>
              </div>

              {/* Second row: Email and Phone */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email" className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    Email
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={contactData.email}
                    onChange={(e) =>
                      handleContactChange("email", e.target.value)
                    }
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone" className="flex items-center gap-2">
                    <Phone className="h-4 w-4" />
                    Telefon
                  </Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={contactData.phone}
                    onChange={(e) =>
                      handleContactChange("phone", e.target.value)
                    }
                    placeholder="+48 123 456 789"
                  />
                </div>
              </div>

              {/* Third row: Address (full width) */}
              <div className="space-y-2">
                <Label htmlFor="address" className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Adres
                </Label>
                <Input
                  id="address"
                  value={contactData.address}
                  onChange={(e) =>
                    handleContactChange("address", e.target.value)
                  }
                  placeholder="Ulica, miasto, kod pocztowy"
                />
              </div>

              {/* Fourth row: Notes (full width) */}
              <div className="space-y-2">
                <Label htmlFor="notes" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Notatki
                </Label>
                <textarea
                  id="notes"
                  value={contactData.notes}
                  onChange={(e) => handleContactChange("notes", e.target.value)}
                  placeholder="Dodatkowe informacje, notatki..."
                  rows={3}
                  className="w-full min-h-[80px] px-3 py-2 border border-input bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-md resize-none"
                />
              </div>

              <div className="flex justify-end pt-4">
                <Button
                  onClick={() => saveContactData(contactData)}
                  disabled={isSavingContact}
                  className="flex items-center gap-2"
                >
                  {isSavingContact ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Zapisywanie...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4" />
                      Zapisz dane kontaktowe
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Metadata Manager */}
        <MetadataManager
          domainId={domain.id}
          initialMetadata={domain.metadata}
          onMetadataUpdate={(metadata) => {
            setDomain((prev) => (prev ? { ...prev, metadata } : null));
          }}
        />

        {/* Email History */}
        <EmailHistory domainId={domain.id} />

        {/* Mail Tracking Stats */}
        <MailTrackingStats domainId={domain.id} />
      </div>

      {/* Lighthouse Modal */}
      <Dialog open={showLighthouseModal} onOpenChange={setShowLighthouseModal}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Wyniki Lighthouse - {domain.domain}
            </DialogTitle>
          </DialogHeader>
          <ScrollArea className="max-h-[60vh] w-full">
            <div className="p-4">
              {lighthouseContent ? (
                <div className="prose prose-sm max-w-none dark:prose-invert">
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm]}
                    components={{
                      // Custom styling for code blocks
                      code: ({ className, children, ...props }: React.ComponentProps<'code'>) => {
                        const isInline = !className?.includes('language-');
                        return isInline ? (
                          <code
                            className="bg-muted px-1 py-0.5 rounded text-sm font-mono"
                            {...props}
                          >
                            {children}
                          </code>
                        ) : (
                          <code
                            className="block bg-muted p-4 rounded-lg overflow-x-auto text-sm font-mono whitespace-pre"
                            {...props}
                          >
                            {children}
                          </code>
                        );
                      },
                      // Custom styling for tables
                      table: ({ children }: React.ComponentProps<'table'>) => (
                        <div className="overflow-x-auto">
                          <table className="min-w-full border-collapse border border-border">
                            {children}
                          </table>
                        </div>
                      ),
                      th: ({ children }: React.ComponentProps<'th'>) => (
                        <th className="border border-border bg-muted px-4 py-2 text-left font-semibold">
                          {children}
                        </th>
                      ),
                      td: ({ children }: React.ComponentProps<'td'>) => (
                        <td className="border border-border px-4 py-2">
                          {children}
                        </td>
                      ),
                    }}
                  >
                    {lighthouseContent}
                  </ReactMarkdown>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Brak danych Lighthouse dla tej domeny</p>
                </div>
              )}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
      </div>

      {/* Related Domains Sidebar */}
      <div className="hidden xl:block">
        <RelatedDomainsSidebar currentDomain={domain} />
      </div>
    </div>
  );
}
