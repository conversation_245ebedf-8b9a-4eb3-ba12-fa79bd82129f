/**
 * Utility functions for checking CMS type of domains
 */

export interface CmsCheckResult {
  cms: 'wordpress' | 'inny';
  status: 'success' | 'error';
  httpStatus?: number;
  error?: string;
}

/**
 * Check CMS type for a single domain
 * @param fullDomain - Full domain URL (e.g., "https://example.com")
 * @param timeout - Timeout in milliseconds (default: 5000)
 * @returns Promise<CmsCheckResult>
 */
export async function checkDomainCms(
  fullDomain: string, 
  timeout: number = 5000
): Promise<CmsCheckResult> {
  try {
    const wpLoginUrl = `${fullDomain}/wp-login.php`;
    
    // Wykonaj zapytanie HEAD aby sprawdzić czy strona istnieje
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    const response = await fetch(wpLoginUrl, {
      method: 'HEAD',
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; CMS-Checker/1.0)'
      }
    });

    clearTimeout(timeoutId);

    // Je<PERSON>li strona wp-login.php istnieje (status 200-299), to prawdopodobnie WordPress
    if (response.ok) {
      return {
        cms: 'wordpress',
        status: 'success',
        httpStatus: response.status
      };
    } else {
      return {
        cms: 'inny',
        status: 'success',
        httpStatus: response.status
      };
    }
  } catch (error) {
    // W przypadku błędu (timeout, błąd sieci itp.) oznacz jako 'inny'
    return {
      cms: 'inny',
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}