import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';
import EmailHistory from '@/models/EmailHistory';

// GET endpoint to fetch specific email content from history
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; historyId: string }> }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { id, historyId } = await params;

    // Verify domain ownership
    const domain = await Domain.findOne({
      _id: id,
      userId: session.user.id
    });

    if (!domain) {
      return NextResponse.json(
        {
          success: false,
          error: 'Domain not found or access denied'
        },
        { status: 404 }
      );
    }

    // Find email history entry
    const emailHistory = await EmailHistory.findOne({
      _id: historyId,
      domainId: id,
      userId: session.user.id
    });

    if (!emailHistory) {
      return NextResponse.json(
        {
          success: false,
          error: 'Email history entry not found'
        },
        { status: 404 }
      );
    }

    // Return email content
    return NextResponse.json({
      success: true,
      data: {
        id: emailHistory._id,
        recipientEmail: emailHistory.recipientEmail,
        subject: emailHistory.subject,
        emailType: emailHistory.emailType,
        status: emailHistory.status,
        sentAt: emailHistory.sentAt,
        emailContent: emailHistory.emailContent || '',
        metadata: emailHistory.metadata,
        createdAt: emailHistory.createdAt,
        updatedAt: emailHistory.updatedAt
      },
      domain: {
        id: domain._id,
        domain: domain.domain,
        fullDomain: domain.fullDomain
      }
    });

  } catch (error) {
    console.error('Error fetching email content:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error occurred while fetching email content'
      },
      { status: 500 }
    );
  }
}
