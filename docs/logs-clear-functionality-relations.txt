FUNKCJONALNOŚĆ CZYSZCZENIA WSZYSTKICH LOGÓW
==========================================

OPIS:
=====
Dodano przycisk "Wyczyść wszystkie logi" na stronie dashboard/logs, który pozwala na usunięcie wszystkich logów systemowych z bazy danych.

KOMPONENTY I PLIKI:
==================

1. API Endpoint - /api/logs/route.ts
   - Dodano metodę DELETE
   - Autoryzacja: Sprawdza session.user.id
   - Operacja: Log.deleteMany({}) - usuwa wszystkie logi
   - Zwraca: success, message, deletedCount

2. Frontend - /dashboard/logs/page.tsx
   - Dodano przycisk "Wyczyść wszystkie logi" w nagłówku
   - AlertDialog z potwierdzeniem usunięcia
   - Stan: showClearDialog, isClearing
   - Funkcja: handleClearAllLogs()

RELACJE MIĘDZY KOMPONENTAMI:
============================

LogsPage Component
├── Importy: AlertDialog, Trash2, toast
├── Stan: showClearDialog, isClearing
├── Funkcja: handleClearAllLogs()
├── UI: Przycisk "Wyczyść wszystkie logi" z ikoną Trash2
├── Dialog: AlertDialog z potwierdzeniem usunięcia
└── Feedback: Toast notifications, refresh logów po usunięciu

API DELETE /api/logs
├── Autoryzacja: Sprawdza session.user.id
├── Operacje bazy danych: Log.deleteMany({})
├── Odpowiedź: Informacje o liczbie usuniętych logów
└── Obsługa błędów: Zwraca odpowiednie kody statusu

KOMPONENTY UI SHADCN:
====================

- AlertDialog - główny kontener dialogu
- AlertDialogTrigger - przycisk wywołujący dialog
- AlertDialogContent - zawartość dialogu
- AlertDialogHeader - nagłówek dialogu
- AlertDialogTitle - tytuł dialogu
- AlertDialogDescription - opis akcji
- AlertDialogFooter - stopka z przyciskami
- AlertDialogCancel - przycisk anulowania
- AlertDialogAction - przycisk potwierdzenia
- Button (variant="destructive") - przycisk usuwania
- Ikona Trash2 z lucide-react
- Loader2 - animacja ładowania podczas usuwania

OBSŁUGA BŁĘDÓW:
===============

- Sprawdzanie autoryzacji użytkownika
- Obsługa błędów sieciowych
- Wyświetlanie komunikatów błędów przez toast
- Zabezpieczenie przed wielokrotnym kliknięciem (isClearing)
- Wyłączenie przycisku gdy brak logów (pagination.totalCount === 0)

INTEGRACJA Z ISTNIEJĄCYM KODEM:
===============================

- Wykorzystuje istniejący system autoryzacji
- Używa tego samego modelu Log co inne operacje
- Integruje się z systemem paginacji
- Używa istniejącego systemu toast notifications
- Odświeża listę logów po usunięciu (fetchLogs(1))

BEZPIECZEŃSTWO:
===============

- Wymaga autoryzacji użytkownika
- Potwierdzenie akcji przez AlertDialog
- Wyświetla liczbę logów do usunięcia
- Informuje o nieodwracalności operacji
- Zabezpieczenie przed przypadkowym kliknięciem

FLOW UŻYTKOWNIKA:
=================

1. Użytkownik klika "Wyczyść wszystkie logi"
2. Wyświetla się dialog z potwierdzeniem
3. Dialog pokazuje liczbę logów do usunięcia
4. Użytkownik potwierdza lub anuluje
5. Jeśli potwierdzi - wysyłane jest żądanie DELETE
6. Wyświetla się komunikat o sukcesie/błędzie
7. Lista logów zostaje odświeżona
8. Dialog zostaje zamknięty

TESTOWANIE:
===========

- Sprawdzić działanie przycisku gdy są logi
- Sprawdzić wyłączenie przycisku gdy brak logów
- Sprawdzić dialog potwierdzenia
- Sprawdzić komunikaty toast
- Sprawdzić odświeżenie listy po usunięciu
- Sprawdzić obsługę błędów sieciowych
- Sprawdzić autoryzację (brak sesji)
